/**
 * Admin Dashboard JavaScript
 * Handles tab switching and other interactive functionality
 */

// Flags to track if data has been loaded
let socketDataLoaded = false;
let cpuDataStatsLoaded = false;
let cpuModelsLoaded = false;
let customRulesLoaded = false;
let finalDataLoaded = false;
let cpuDetailsLoaded = false;
let amazonSearchTermsLoaded = false;

// Flag to prevent multiple redirects on session expiration
let redirectInProgress = false;

// Current rule ID for delete operation (global scope)
let currentRuleId = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize CPU Data functionality
    initCpuDataManagement();

    // Initialize inner tabs functionality
    initInnerTabs();

    // Initialize CPU details functionality
    initCpuDetailsManagement();

    // Close modals on Escape key press
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close all modals
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        }
    });

    // Tab switching functionality
    const tabLinks = document.querySelectorAll('.tab-navigation a');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the tab ID from data attribute
            const tabId = this.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            tabLinks.forEach(link => link.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to current tab and content
            this.classList.add('active');
            document.getElementById(tabId).classList.add('active');

            // Save active tab to localStorage for persistence
            localStorage.setItem('activeAdminTab', tabId);

            // Load data when tabs are activated (only if not loaded before)
            if (tabId === 'socket-data') {
                if (!socketDataLoaded) {
                    loadSocketData();
                    socketDataLoaded = true;
                }
                // Always try to load CPU socket tags when socket tab is activated
                loadCpuSocketTags();
            }

            // Load CPU data statistics and models when CPU data tab is activated
            if (tabId === 'cpu-data') {
                loadCpuDataStats();
                loadCpuModels();
            }

            // Load Amazon Search Terms when Amazon Search tab is activated
            if (tabId === 'amazon-search') {
                if (!amazonSearchTermsLoaded) {
                    loadAmazonSearchTerms();
                    amazonSearchTermsLoaded = true;
                }
            }
        });
    });

    // Restore active tab from localStorage if available
    const activeTab = localStorage.getItem('activeAdminTab');
    if (activeTab) {
        const activeTabLink = document.querySelector(`.tab-navigation a[data-tab="${activeTab}"]`);
        if (activeTabLink) {
            // Trigger a click on the active tab link
            activeTabLink.click();
        }
    } else {
        // Set "Socket Data" as the default tab if no tab is saved
        localStorage.setItem('activeAdminTab', 'socket-data');
    }

    // Socket Management Functionality
    initSocketManagement();

    // Initialize Amazon Search Term Management
    initAmazonSearchTermManagement();
});

/**
 * Open the search term modal for adding a new term
 */
function openAddSearchTermModal() {
    const modal = document.getElementById('search-term-modal');
    const form = document.getElementById('search-term-form');
    const title = document.getElementById('search-term-modal-title');
    const actionInput = document.getElementById('search-term-action');
    const idInput = document.getElementById('search-term-id');

    if (!modal || !form) return;

    // Reset form
    form.reset();

    // Set title and action
    title.textContent = 'Add Manual Search Term';
    actionInput.value = 'add_manual';
    idInput.value = '';

    // Show modal
    modal.style.display = 'block';
}

/**
 * Open the search term modal for editing an existing term
 * @param {number} id - The ID of the search term to edit
 */
function openEditSearchTermModal(id) {
    const modal = document.getElementById('search-term-modal');
    const form = document.getElementById('search-term-form');
    const title = document.getElementById('search-term-modal-title');
    const actionInput = document.getElementById('search-term-action');
    const idInput = document.getElementById('search-term-id');
    const brandInput = document.getElementById('search-term-brand');
    const keywordsInput = document.getElementById('search-term-keywords');
    const generatedInput = document.getElementById('search-term-generated');

    if (!modal || !form || !id) return;

    // Reset form
    form.reset();

    // Set title and action
    title.textContent = 'Edit Manual Search Term';
    actionInput.value = 'update_manual';
    idInput.value = id;

    // Show loading state
    modal.style.display = 'block';
    form.classList.add('loading');

    // Get CSRF token
    const csrfTokenElement = document.getElementById('search-term-csrf-token');
    let csrfToken = '';
    if (csrfTokenElement) {
        csrfToken = csrfTokenElement.value;
    }

    // Fetch search term data
    fetch(`/api/amazon-search-terms.php?action=get_by_id&id=${id}`, {
        method: 'GET',
        headers: {
            'X-CSRF-Token': csrfToken
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            // Populate form
            brandInput.value = data.data.brand_tag || '';
            keywordsInput.value = data.data.keywords || '';
            generatedInput.value = data.data.generated_search_term || '';
        } else {
            showNotification(data.message || 'Failed to load search term data.', 'error');
            closeSearchTermModal();
        }
    })
    .catch(error => {
        console.error('Error fetching search term data:', error);
        showNotification('Error loading search term data.', 'error');
        closeSearchTermModal();
    })
    .finally(() => {
        form.classList.remove('loading');
    });
}

/**
 * Close the search term modal
 */
function closeSearchTermModal() {
    const modal = document.getElementById('search-term-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Handle search term form submission
 * @param {Event} e - The form submit event
 */
function handleSearchTermFormSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const submitBtn = document.getElementById('search-term-submit');

    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Saving...';
    }

    const formData = new FormData(form);

    fetch('/api/amazon-search-terms.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Search term saved successfully.', 'success');
            closeSearchTermModal();
            loadAmazonSearchTerms(true); // Force reload
        } else {
            showNotification(data.message || 'Failed to save search term.', 'error');
        }
    })
    .catch(error => {
        console.error('Error saving search term:', error);
        showNotification('Error saving search term.', 'error');
    })
    .finally(() => {
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Save';
        }
    });
}

/**
 * Toggle the active status of a search term
 * @param {number} id - The ID of the search term to toggle
 */
function toggleSearchTermActive(id) {
    if (!id) return;

    // Get CSRF token
    const csrfTokenElement = document.getElementById('search-term-csrf-token') || document.querySelector('meta[name="csrf-token"]');
    let csrfToken = '';
    if (csrfTokenElement) {
        csrfToken = csrfTokenElement.value || csrfTokenElement.content;
    }

    const formData = new FormData();
    formData.append('action', 'toggle_active');
    formData.append('id', id);
    if (csrfToken) {
        formData.append('csrf_token', csrfToken);
    }

    fetch('/api/amazon-search-terms.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Search term status updated successfully.', 'success');
            loadAmazonSearchTerms(true); // Force reload
        } else {
            showNotification(data.message || 'Failed to update search term status.', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating search term status:', error);
        showNotification('Error updating search term status.', 'error');
    });
}

/**
 * Show the delete confirmation modal for a search term
 * @param {number} id - The ID of the search term to delete
 */
function confirmDeleteSearchTerm(id) {
    if (!id) return;

    // Store the ID to be deleted
    window.currentSearchTermId = id;

    // Show the delete confirmation modal
    const modal = document.getElementById('search-term-delete-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * Delete a search term
 */
function deleteSearchTerm() {
    const id = window.currentSearchTermId;
    if (!id) {
        showNotification('No search term selected for deletion.', 'error');
        return;
    }

    // Get CSRF token
    const csrfTokenElement = document.getElementById('search-term-csrf-token') || document.querySelector('meta[name="csrf-token"]');
    let csrfToken = '';
    if (csrfTokenElement) {
        csrfToken = csrfTokenElement.value || csrfTokenElement.content;
    }

    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('id', id);
    if (csrfToken) {
        formData.append('csrf_token', csrfToken);
    }

    fetch('/api/amazon-search-terms.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        // Close the delete confirmation modal
        const modal = document.getElementById('search-term-delete-modal');
        if (modal) {
            modal.style.display = 'none';
        }

        if (data.success) {
            showNotification(data.message || 'Search term deleted successfully.', 'success');
            loadAmazonSearchTerms(true); // Force reload
        } else {
            showNotification(data.message || 'Failed to delete search term.', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting search term:', error);
        showNotification('Error deleting search term.', 'error');

        // Close the delete confirmation modal
        const modal = document.getElementById('search-term-delete-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    });
}

/**
 * Initialize Amazon Search Term Management functionality
 */
function initAmazonSearchTermManagement() {
    const amazonSearchTab = document.getElementById('amazon-search');
    if (!amazonSearchTab) return;

    // Generate button
    const generateBtn = document.getElementById('generate-amazon-search-terms-btn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateAmazonSearchTerms);
    }

    // Add manual search term button
    const addManualBtn = document.getElementById('add-manual-search-term-btn');
    if (addManualBtn) {
        addManualBtn.addEventListener('click', openAddSearchTermModal);
    }

    // Search term modal close button
    const modalCloseBtn = document.querySelector('#search-term-modal .modal-close');
    if (modalCloseBtn) {
        modalCloseBtn.addEventListener('click', closeSearchTermModal);
    }

    // Search term modal cancel button
    const modalCancelBtn = document.getElementById('search-term-cancel');
    if (modalCancelBtn) {
        modalCancelBtn.addEventListener('click', closeSearchTermModal);
    }

    // Search term form submission
    const searchTermForm = document.getElementById('search-term-form');
    if (searchTermForm) {
        searchTermForm.addEventListener('submit', handleSearchTermFormSubmit);
    }



    // Event delegation for edit, toggle, and delete buttons
    const termsTable = document.getElementById('amazon-search-terms-list');
    if (termsTable) {
        termsTable.addEventListener('click', function(e) {
            // Edit button
            if (e.target.closest('.edit-search-term')) {
                const button = e.target.closest('.edit-search-term');
                const id = button.getAttribute('data-id');
                if (id) {
                    openEditSearchTermModal(id);
                }
            }

            // Toggle button
            if (e.target.closest('.toggle-search-term')) {
                const button = e.target.closest('.toggle-search-term');
                const id = button.getAttribute('data-id');
                if (id) {
                    toggleSearchTermActive(id);
                }
            }

            // Delete button
            if (e.target.closest('.delete-search-term')) {
                const button = e.target.closest('.delete-search-term');
                const id = button.getAttribute('data-id');
                if (id) {
                    confirmDeleteSearchTerm(id);
                }
            }
        });
    }

    // Delete confirmation modal
    const confirmDeleteBtn = document.getElementById('confirm-delete-search-term');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', deleteSearchTerm);
    }

    const cancelDeleteBtn = document.getElementById('cancel-delete-search-term');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            const modal = document.getElementById('search-term-delete-modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    }



    // Close delete modal when clicking the X
    const deleteModalClose = document.querySelector('#search-term-delete-modal .modal-close');
    if (deleteModalClose) {
        deleteModalClose.addEventListener('click', function() {
            const modal = document.getElementById('search-term-delete-modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    }

    // Load search terms if the tab is active
    if (amazonSearchTab.classList.contains('active')) {
        if (!amazonSearchTermsLoaded) {
            loadAmazonSearchTerms();
            amazonSearchTermsLoaded = true;
        }
    }
}

/**
 * Load Amazon search terms from the API
 * @param {boolean} forceReload - Whether to force reload the data
 */
function loadAmazonSearchTerms(forceReload = false) {
    const termsListTable = document.getElementById('amazon-search-terms-list');
    if (!termsListTable) return;

    const tbody = termsListTable.querySelector('tbody');
    if (!tbody) return;

    if (amazonSearchTermsLoaded && !forceReload) {
        return;
    }

    tbody.innerHTML = '<tr class="loading-row"><td colspan="8">Loading search terms...</td></tr>';

    const csrfTokenElement = document.getElementById('socket-csrf-token') || document.querySelector('meta[name="csrf-token"]');
    let csrfToken = '';
    if (csrfTokenElement) {
        csrfToken = csrfTokenElement.value || csrfTokenElement.content;
    }

    let headers = {};
    let url = '/api/amazon-search-terms.php?action=get_all';

    if (csrfToken) {
        headers['X-CSRF-Token'] = csrfToken;
        // It's good practice to include CSRF in POST body for POST, and header/query for GET.
        // For GET, header is preferred if server supports it, otherwise query param.
        // Let's assume header is fine for GET here.
    }

    fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            displayAmazonSearchTerms(data.data);
            amazonSearchTermsLoaded = true;
        } else {
            // Update the count to 0 in case of error
            const countElement = document.getElementById('amazon-search-term-count');
            if (countElement) {
                countElement.textContent = '(0)';
            }

            tbody.innerHTML = `<tr class="no-data-row"><td colspan="8">Error: ${data.message || 'Failed to load search terms.'}</td></tr>`;
            showNotification(data.message || 'Failed to load search terms.', 'error');
        }
    })
    .catch(error => {
        console.error('Error fetching Amazon search terms:', error);

        // Update the count to 0 in case of error
        const countElement = document.getElementById('amazon-search-term-count');
        if (countElement) {
            countElement.textContent = '(0)';
        }

        tbody.innerHTML = '<tr class="no-data-row"><td colspan="8">Error loading search terms. Please try again.</td></tr>';
        showNotification('Error loading search terms.', 'error');
    });
}

/**
 * Display Amazon search terms in the table
 * @param {Array} terms - Array of search term objects
 */
function displayAmazonSearchTerms(terms) {
    const termsListTable = document.getElementById('amazon-search-terms-list');
    if (!termsListTable) return;

    const tbody = termsListTable.querySelector('tbody');
    if (!tbody) return;

    // Update the count in the section title
    const countElement = document.getElementById('amazon-search-term-count');
    if (countElement) {
        countElement.textContent = `(${terms ? terms.length : 0})`;
    }

    if (!terms || terms.length === 0) {
        tbody.innerHTML = '<tr class="no-data-row"><td colspan="8">No search terms found. Click "Generate Search Terms" to create them.</td></tr>';
        return;
    }

    tbody.innerHTML = '';
    terms.forEach(term => {
        const row = document.createElement('tr');
        // Default to active if the active property is missing
        const isActive = term.active === undefined ? true : term.active == 1;
        // Default to auto if the type property is missing
        const isManual = term.type === undefined ? false : term.type === 'manual';

        row.innerHTML = `
            <td>${term.id}</td>
            <td>${escapeHtml(term.brand_tag || '')}</td>
            <td>${escapeHtml(term.keywords || '')}</td>
            <td>${escapeHtml(term.generated_search_term)}</td>
            <td><span class="term-type ${isManual ? 'manual' : 'auto'}">${isManual ? 'Manual' : 'Auto'}</span></td>
            <td>${escapeHtml(term.product_count || '')}</td>
            <td>${new Date(term.created_at).toLocaleString()}</td>
            <td class="actions-cell">
                ${isManual ? `
                <button class="icon-button edit-search-term" data-id="${term.id}" title="Edit Search Term">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                </button>` : ''}
                <button class="icon-button toggle-search-term ${isActive ? 'active' : 'inactive'}" data-id="${term.id}" title="${isActive ? 'Disable' : 'Enable'} Search Term">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        ${isActive ?
                            '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>' :
                            '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>'
                        }
                    </svg>
                </button>
                <button class="icon-button delete-search-term" data-id="${term.id}" title="Delete Search Term">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                </button>
            </td>
        `;

        if (!isActive) {
            row.classList.add('inactive-row');
        }

        tbody.appendChild(row);
    });
}

/**
 * Generate Amazon search terms
 */
function generateAmazonSearchTerms() {
    const generateBtn = document.getElementById('generate-amazon-search-terms-btn');
    if (generateBtn) {
        generateBtn.disabled = true;
        generateBtn.textContent = 'Generating...';
    }

    const csrfTokenElement = document.getElementById('socket-csrf-token') || document.querySelector('meta[name="csrf-token"]');
    let csrfToken = '';
    if (csrfTokenElement) {
        csrfToken = csrfTokenElement.value || csrfTokenElement.content;
    }

    const formData = new FormData();
    formData.append('action', 'generate');
    if (csrfToken) {
        formData.append('csrf_token', csrfToken);
    }

    fetch('/api/amazon-search-terms.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || `Successfully generated ${data.count} search terms.`, 'success');
            loadAmazonSearchTerms(true); // Force reload
        } else {
            showNotification(data.message || 'Failed to generate search terms.', 'error');
        }
    })
    .catch(error => {
        console.error('Error generating Amazon search terms:', error);
        showNotification('Error generating search terms.', 'error');
    })
    .finally(() => {
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.textContent = 'Generate Search Terms';
        }
    });
}

/**
 * Initialize inner tabs functionality
 */
function initInnerTabs() {
    const innerTabLinks = document.querySelectorAll('.inner-tab-link');
    const innerTabContents = document.querySelectorAll('.inner-tab-content');

    if (!innerTabLinks.length || !innerTabContents.length) return;

    // Add click event to each inner tab link
    innerTabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the inner tab ID from data attribute
            const innerTabId = this.getAttribute('data-inner-tab');

            // Remove active class from all inner tabs and contents
            innerTabLinks.forEach(link => link.classList.remove('active'));
            innerTabContents.forEach(content => content.classList.remove('active'));

            // Add active class to current inner tab and content
            this.classList.add('active');
            document.getElementById(innerTabId).classList.add('active');

            // Save active inner tab to localStorage for persistence
            localStorage.setItem('activeCpuDataInnerTab', innerTabId);

            // Load CPU models when the review tab is activated (only if not loaded before)
            if (innerTabId === 'review-tab' && !cpuModelsLoaded) {
                loadCpuModels();
            }
            // Load custom rules when the custom rules tab is activated (only if not loaded before)
            else if (innerTabId === 'custom-rules-tab' && !customRulesLoaded) {
                loadCustomRules();
            }
            // Load final data when the final data tab is activated (only if not loaded before)
            else if (innerTabId === 'final-data-tab' && !finalDataLoaded) {
                loadFinalData();
            }
            // Load CPU details when the CPU details tab is activated (only if not loaded before)
            else if (innerTabId === 'cpu-details-tab') {
                // Always reload CPU details when tab is activated to ensure fresh data
                cpuDetailsLoaded = false;
                loadCpuDetails();
                cpuDetailsLoaded = true;
            }
        });
    });

    // Restore active inner tab from localStorage if available
    const activeInnerTab = localStorage.getItem('activeCpuDataInnerTab');
    if (activeInnerTab) {
        const activeInnerTabLink = document.querySelector(`.inner-tab-link[data-inner-tab="${activeInnerTab}"]`);
        if (activeInnerTabLink) {
            // Trigger a click on the active inner tab link
            activeInnerTabLink.click();
        }
    }
}

/**
 * Initialize Socket Management functionality
 */
function initSocketManagement() {
    // Elements
    const socketTab = document.getElementById('socket-data');
    if (!socketTab) return; // Exit if socket tab doesn't exist

    const addSocketBtn = document.getElementById('add-socket-btn');
    const socketModal = document.getElementById('socket-modal');
    const socketForm = document.getElementById('socket-form');
    const socketFormCancel = document.getElementById('socket-form-cancel');
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    const addVariantBtn = document.getElementById('add-variant-btn');
    const deleteModal = document.getElementById('delete-modal');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
    const notification = document.getElementById('notification');
    const notificationClose = document.querySelector('.notification-close');

    // Event Listeners
    if (addSocketBtn) {
        addSocketBtn.addEventListener('click', () => openSocketModal());
    }

    if (socketFormCancel) {
        socketFormCancel.addEventListener('click', () => closeSocketModal());
    }

    if (modalCloseButtons) {
        modalCloseButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) modal.style.display = 'none';
            });
        });
    }

    if (addVariantBtn) {
        addVariantBtn.addEventListener('click', addVariantField);
    }

    if (socketForm) {
        socketForm.addEventListener('submit', handleSocketFormSubmit);
    }

    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', handleDelete);
    }

    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', () => {
            deleteModal.style.display = 'none';
        });
    }

    if (notificationClose) {
        notificationClose.addEventListener('click', () => {
            notification.style.display = 'none';
        });
    }

    // Filter event listeners removed as filters are no longer needed



    // Initialize socket data if socket tab is active
    if (socketTab.classList.contains('active')) {
        // Load socket data if not loaded before
        if (!socketDataLoaded) {
            loadSocketData();
            socketDataLoaded = true;
        }

        // Always try to load CPU socket tags when socket tab is active
        // This ensures the tags are displayed even if the socket data is already loaded
        setTimeout(loadCpuSocketTags, 300);
    }
}

/**
 * Load and display socket values from CPU data
 */
function loadCpuSocketTags() {
    console.log('loadCpuSocketTags called');
    const cpuSocketTags = document.getElementById('cpu-socket-tags');
    if (!cpuSocketTags) {
        console.error('cpu-socket-tags element not found');
        return;
    }

    // If CPU models data is already loaded, use it
    if (window.cpuModelsData && Array.isArray(window.cpuModelsData)) {
        console.log('Using existing CPU models data:', window.cpuModelsData.length, 'models');
        displayCpuSocketTags(window.cpuModelsData);
        return;
    }

    console.log('Fetching CPU data for socket tags');
    // Otherwise, fetch CPU data
    cpuSocketTags.innerHTML = '<div class="loading-message">Loading socket values...</div>';

    // Get CSRF token
    const csrfTokenElement = document.getElementById('socket-csrf-token');
    let headers = {};
    let url = '/api/cpu-data.php?action=list';

    if (csrfTokenElement) {
        headers['X-CSRF-Token'] = csrfTokenElement.value;
        url += '&csrf_token=' + encodeURIComponent(csrfTokenElement.value);
    }

    console.log('Fetching CPU data from URL:', url);

    fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('API response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('API response data:', data);
        if (data.success && data.data && Array.isArray(data.data)) {
            console.log('CPU data loaded successfully, found', data.data.length, 'models');
            // Store the CPU models data in a global variable if not already stored
            if (!window.cpuModelsData) {
                window.cpuModelsData = data.data;
            }
            displayCpuSocketTags(data.data);
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            console.error('API response format error:', data);
            cpuSocketTags.innerHTML = '<div class="error-message">Error loading socket values.</div>';
        }
    })
    .catch(error => {
        console.error('Error fetching CPU data for socket tags:', error);
        cpuSocketTags.innerHTML = '<div class="error-message">Error loading socket values.</div>';
    });
}

/**
 * Display socket tags from CPU data
 * @param {Array} cpuModels - Array of CPU model objects
 */
function displayCpuSocketTags(cpuModels) {
    console.log('displayCpuSocketTags called with', cpuModels ? cpuModels.length : 0, 'models');
    const cpuSocketTags = document.getElementById('cpu-socket-tags');
    if (!cpuSocketTags) {
        console.error('cpu-socket-tags element not found in displayCpuSocketTags');
        return;
    }

    // Get unique socket values and trim any extra spaces
    const sockets = [...new Set(cpuModels.map(model => {
        // Handle cases where socket might be missing, null, or empty
        if (!model.socket) return '';
        return typeof model.socket === 'string' ? model.socket.trim() : String(model.socket).trim();
    }))]
        .filter(Boolean) // Remove empty values
        .sort(); // Sort alphabetically

    console.log('Found', sockets.length, 'unique socket values:', sockets);

    if (sockets.length === 0) {
        // Update the heading to show zero count
        const socketOverviewHeading = document.querySelector('.socket-overview h4');
        if (socketOverviewHeading) {
            socketOverviewHeading.textContent = 'Socket Values in CPU Data (0)';
        }

        cpuSocketTags.innerHTML = '<div class="info-message">No socket values found in CPU data.</div>';
        return;
    }

    // Get all socket variants from the socket data
    const socketVariants = getAllSocketVariants();
    console.log('Socket variants:', socketVariants);

    // Create HTML for socket tags
    let html = '';
    let matchedCount = 0;

    sockets.forEach(socket => {
        const isMatched = isSocketMatchedByVariant(socket, socketVariants);
        if (isMatched) matchedCount++;
        console.log(`Socket "${socket}" matched:`, isMatched);
        html += `<span class="socket-tag ${isMatched ? 'matched' : ''}" title="${isMatched ? 'Matched by socket rules' : 'Not matched by any socket rule'}">${socket}</span>`;
    });

    // Update the heading to include the matched count
    const socketOverviewHeading = document.querySelector('.socket-overview h4');
    if (socketOverviewHeading) {
        socketOverviewHeading.textContent = `Socket Values in CPU Data (${sockets.length}, ${matchedCount} matched)`;
    }

    console.log('Setting innerHTML for socket tags');
    cpuSocketTags.innerHTML = html;
}

/**
 * Get all socket variants from the socket data
 * @returns {Array} Array of all socket variant names
 */
function getAllSocketVariants() {
    console.log('getAllSocketVariants called');

    // If socket data is not loaded yet, return empty array
    if (!window.socketData || !Array.isArray(window.socketData)) {
        console.log('No socket data available yet');
        return [];
    }

    console.log('Socket data available:', window.socketData.length, 'sockets');

    // Collect all variants from all sockets
    const variants = [];
    window.socketData.forEach(socket => {
        console.log(`Processing socket: ${socket.name}, variants:`, socket.variants);
        if (socket.variants && Array.isArray(socket.variants)) {
            socket.variants.forEach(variant => {
                const variantName = variant.name || variant.variant_name || '';
                if (variantName) {
                    console.log(`Adding variant: ${variantName}`);
                    variants.push(variantName.toLowerCase());
                }
            });
        }
    });

    console.log('Collected variants:', variants);
    return variants;
}

/**
 * Check if a socket is matched by any socket variant
 * @param {string} socket - Socket name to check
 * @param {Array} variants - Array of variant names
 * @returns {boolean} True if socket is matched by any variant
 */
function isSocketMatchedByVariant(socket, variants) {
    if (!socket || !variants || !Array.isArray(variants)) {
        return false;
    }

    // Convert socket to lowercase for case-insensitive comparison
    const socketLower = socket.toLowerCase();

    // Check if socket exactly matches any variant
    return variants.includes(socketLower);
}

/**
 * Load socket data from the API
 * @param {boolean} forceReload - Whether to force reload the data even if it was loaded before
 */
function loadSocketData(forceReload = false) {
    // If data is already loaded and we're not forcing a reload, return
    if (socketDataLoaded && !forceReload) {
        return;
    }
    const socketList = document.getElementById('socket-list');
    if (!socketList) return;

    const tbody = socketList.querySelector('tbody');
    tbody.innerHTML = '<tr class="loading-row"><td colspan="7">Loading socket data...</td></tr>';

    // Get CSRF token from the socket-csrf-token element if available
    const csrfTokenElement = document.getElementById('socket-csrf-token');
    let headers = {};
    let url = '/api/sockets.php?action=list';

    if (csrfTokenElement) {
        // Add CSRF token as a single header
        // Browsers normalize header names, so we only need to set it once
        headers['X-CSRF-Token'] = csrfTokenElement.value;

        // Also add as URL parameter as fallback
        url += '&csrf_token=' + encodeURIComponent(csrfTokenElement.value);
    } else {
        console.warn('CSRF token element not found, proceeding without CSRF token');
    }

    fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            displaySocketData(data.data);
            socketDataLoaded = true;

            // Try to load CPU socket tags after socket data is loaded
            loadCpuSocketTags();
        } else {
            let errorMessage = data.message;

            // Check if this is an unauthorized access error (session expired)
            if (errorMessage === 'Unauthorized access') {
                handleUnauthorizedAccess();
                return;
            }

            // If there's debug output, log it to console and add a button to view it
            if (data.debug_output) {
                console.error('Debug output:', data.debug_output);

                tbody.innerHTML = `
                    <tr class="no-data-row">
                        <td colspan="7">
                            <div class="error-container">
                                <p>Error: ${data.message}</p>
                                <details>
                                    <summary>Show Debug Info</summary>
                                    <pre>${data.debug_output}</pre>
                                </details>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                tbody.innerHTML = `<tr class="no-data-row"><td colspan="7">Error: ${data.message}</td></tr>`;
            }

            showNotification(errorMessage, 'error');
        }
    })
    .catch(error => {
        console.error('Error fetching socket data:', error);
        tbody.innerHTML = `
            <tr class="no-data-row">
                <td colspan="7">
                    <div class="error-container">
                        <p>Error loading socket data. Please try again.</p>
                        <details>
                            <summary>Show Error Details</summary>
                            <pre>${error.toString()}</pre>
                        </details>
                    </div>
                </td>
            </tr>
        `;
        showNotification('Error loading socket data. Please try again.', 'error');
    });
}

/**
 * Display socket data in the table
 *
 * @param {Array} sockets Array of socket objects
 */
function displaySocketData(sockets) {
    const socketList = document.getElementById('socket-list');
    if (!socketList) return;

    const tbody = socketList.querySelector('tbody');

    if (!sockets || sockets.length === 0) {
        tbody.innerHTML = '<tr class="no-data-row"><td colspan="7">No socket data found.</td></tr>';
        return;
    }

    // Store socket data in a global variable for use in other functions
    window.socketData = sockets;

    // Refresh socket tags if CPU data is loaded
    if (window.cpuModelsData && Array.isArray(window.cpuModelsData)) {
        displayCpuSocketTags(window.cpuModelsData);
    }

    // Sort sockets according to specified order:
    // 1. Intel desktop
    // 2. AMD desktop
    // 3. Intel server
    // 4. AMD server
    sockets.sort((a, b) => {
        // Create a priority value for each socket based on manufacturer and type
        const getPriority = (socket) => {
            if (socket.manufacturer === 'Intel' && socket.type === 'desktop') return 1;
            if (socket.manufacturer === 'AMD' && socket.type === 'desktop') return 2;
            if (socket.manufacturer === 'Intel' && socket.type === 'server') return 3;
            if (socket.manufacturer === 'AMD' && socket.type === 'server') return 4;
            return 5; // Any other combination (shouldn't happen)
        };

        const priorityA = getPriority(a);
        const priorityB = getPriority(b);

        // First sort by priority (manufacturer + type)
        if (priorityA !== priorityB) {
            return priorityA - priorityB;
        }

        // If same priority, sort by year (newest first)
        return b.year - a.year;
    });

    tbody.innerHTML = '';

    // Track the current category to add category headers
    let currentCategory = '';

    sockets.forEach(socket => {
        // Determine the category of this socket
        const category = `${socket.manufacturer} ${socket.type}`;

        // If this is a new category, add a header row
        if (category !== currentCategory) {
            currentCategory = category;

            // Create a header row for this category
            const headerRow = document.createElement('tr');
            headerRow.className = 'category-header';
            headerRow.innerHTML = `
                <td colspan="7">${socket.manufacturer} ${socket.type.charAt(0).toUpperCase() + socket.type.slice(1)}</td>
            `;
            tbody.appendChild(headerRow);
        }
        const row = document.createElement('tr');
        row.setAttribute('data-id', socket.id);
        row.setAttribute('data-manufacturer', socket.manufacturer);
        row.setAttribute('data-type', socket.type);

        // Format variants as badges
        const variantBadges = socket.variants.map(variant => {
            // Make sure variant name is defined
            const variantName = variant.name || variant.variant_name || '';
            return `<span class="variant-badge">${variantName}</span>`;
        }).join('');

        row.innerHTML = `
            <td>${socket.name}</td>
            <td>${socket.slug}</td>
            <td>${socket.year}</td>
            <td>${socket.manufacturer}</td>
            <td>${socket.type}</td>
            <td>${variantBadges}</td>
            <td class="action-cell">
                <button class="icon-button edit-rule" title="Edit Socket" onclick="editSocket(${socket.id})">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                </button>
                <button class="icon-button delete-rule" title="Delete Socket" onclick="confirmDeleteSocket(${socket.id}, '${socket.name}')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                </button>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// Filter function removed as filters are no longer needed

/**
 * Open the socket modal for adding a new socket
 */
function openSocketModal(socketId = null) {
    const socketModal = document.getElementById('socket-modal');
    const socketForm = document.getElementById('socket-form');
    const modalTitle = document.getElementById('socket-modal-title');
    const socketIdInput = document.getElementById('socket-id');

    // Reset form
    socketForm.reset();

    // Clear variants except for the first one
    const variantsContainer = document.getElementById('socket-variants-container');
    variantsContainer.innerHTML = `
        <div class="variant-row">
            <input type="text" name="socket-variants[]" placeholder="Variant name">
            <button type="button" class="remove-variant-btn">Remove</button>
        </div>
    `;

    // Add event listeners to remove buttons
    addRemoveVariantListeners();

    if (socketId) {
        // Edit mode
        modalTitle.textContent = 'Edit Socket';
        socketIdInput.value = socketId;

        // Fetch socket data
        const csrfTokenElement = document.getElementById('socket-csrf-token');
        let headers = {};
        let url = `/api/sockets.php?action=get&id=${socketId}`;

        if (csrfTokenElement) {
            // Add CSRF token as a single header
            // Browsers normalize header names, so we only need to set it once
            headers['X-CSRF-Token'] = csrfTokenElement.value;

            // Also add as URL parameter as fallback
            url += `&csrf_token=${encodeURIComponent(csrfTokenElement.value)}`;
        } else {
            console.warn('CSRF token element not found, proceeding without CSRF token');
        }

        fetch(url, {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin' // Include cookies in the request
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const socket = data.data;

                // Fill form with socket data
                document.getElementById('socket-name').value = socket.name;
                document.getElementById('socket-slug').value = socket.slug;
                document.getElementById('socket-year').value = socket.year;
                document.getElementById('socket-manufacturer').value = socket.manufacturer;
                document.getElementById('socket-type').value = socket.type;

                // Clear variants container
                variantsContainer.innerHTML = '';

                // Add variant fields
                if (socket.variants && socket.variants.length > 0) {
                    socket.variants.forEach(variant => {
                        // Make sure variant name is defined
                        const variantName = variant.name || variant.variant_name || '';

                        const variantRow = document.createElement('div');
                        variantRow.className = 'variant-row';
                        variantRow.innerHTML = `
                            <input type="text" name="socket-variants[]" placeholder="Variant name" value="${variantName}">
                            <button type="button" class="remove-variant-btn">Remove</button>
                        `;
                        variantsContainer.appendChild(variantRow);
                    });

                    // Add event listeners to remove buttons
                    addRemoveVariantListeners();
                } else {
                    // Add one empty variant field
                    addVariantField();
                }
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error fetching socket data:', error);
            showNotification('Error loading socket data. Please try again.', 'error');
        });
    } else {
        // Add mode
        modalTitle.textContent = 'Add New Socket';
        socketIdInput.value = '';
    }

    // Show modal
    socketModal.style.display = 'block';
}

/**
 * Close the socket modal
 */
function closeSocketModal() {
    const socketModal = document.getElementById('socket-modal');
    socketModal.style.display = 'none';
}

/**
 * Add a new variant field to the form
 */
function addVariantField() {
    const variantsContainer = document.getElementById('socket-variants-container');

    const variantRow = document.createElement('div');
    variantRow.className = 'variant-row';
    variantRow.innerHTML = `
        <input type="text" name="socket-variants[]" placeholder="Variant name">
        <button type="button" class="remove-variant-btn">Remove</button>
    `;

    variantsContainer.appendChild(variantRow);

    // Add event listener to the new remove button
    const removeBtn = variantRow.querySelector('.remove-variant-btn');
    removeBtn.addEventListener('click', function() {
        variantRow.remove();
    });
}

/**
 * Add event listeners to all remove variant buttons
 */
function addRemoveVariantListeners() {
    const removeButtons = document.querySelectorAll('.remove-variant-btn');

    removeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const variantRow = this.closest('.variant-row');
            variantRow.remove();
        });
    });
}

/**
 * Handle socket form submission
 *
 * @param {Event} e Form submit event
 */
function handleSocketFormSubmit(e) {
    e.preventDefault();

    const socketId = document.getElementById('socket-id').value;
    const socketName = document.getElementById('socket-name').value;
    const socketSlug = document.getElementById('socket-slug').value;
    const socketYear = document.getElementById('socket-year').value;
    const socketManufacturer = document.getElementById('socket-manufacturer').value;
    const socketType = document.getElementById('socket-type').value;

    // Get all variant values
    const variantInputs = document.querySelectorAll('input[name="socket-variants[]"]');
    const variants = Array.from(variantInputs)
        .map(input => input.value.trim())
        .filter(value => value !== ''); // Filter out empty values

    // Validate form
    if (!socketName || !socketSlug || !socketYear || !socketManufacturer || !socketType) {
        showNotification('Please fill in all required fields.', 'error');
        return;
    }

    // Prepare data
    const socketData = {
        name: socketName,
        slug: socketSlug,
        year: parseInt(socketYear),
        manufacturer: socketManufacturer,
        type: socketType,
        variants: variants
    };

    // Get CSRF token if available
    const csrfTokenElement = document.getElementById('socket-csrf-token');
    let headers = {
        'Content-Type': 'application/json'
    };

    // Determine if this is an add or update operation
    const isUpdate = socketId !== '';
    let url = isUpdate
        ? `/api/sockets.php?action=update&id=${socketId}`
        : '/api/sockets.php?action=add';
    const method = isUpdate ? 'PUT' : 'POST';

    if (csrfTokenElement) {
        // Add CSRF token as a single header
        // Browsers normalize header names, so we only need to set it once
        headers['X-CSRF-Token'] = csrfTokenElement.value;

        // Also add as URL parameter as fallback
        url += `&csrf_token=${encodeURIComponent(csrfTokenElement.value)}`;

        // Add CSRF token to the data being sent
        socketData.csrf_token = csrfTokenElement.value;
    } else {
        console.warn('CSRF token element not found, proceeding without CSRF token');
    }

    // Send request
    fetch(url, {
        method: method,
        headers: headers,
        body: JSON.stringify(socketData),
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeSocketModal();
            loadSocketData(true); // Force reload socket data
            // Refresh socket tags after socket data is updated
            setTimeout(loadCpuSocketTags, 500);
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error saving socket:', error);
        showNotification('Error saving socket. Please try again.', 'error');
    });
}

/**
 * Edit a socket
 *
 * @param {number} socketId Socket ID
 */
function editSocket(socketId) {
    openSocketModal(socketId);
}

/**
 * Confirm socket deletion
 *
 * @param {number} socketId Socket ID
 * @param {string} socketName Socket name
 */
function confirmDeleteSocket(socketId, socketName) {
    const deleteModal = document.getElementById('delete-modal');
    const deleteMessage = document.getElementById('delete-message');
    const deleteId = document.getElementById('delete-id');
    const deleteType = document.getElementById('delete-type');

    deleteMessage.textContent = `Are you sure you want to delete the socket "${socketName}"? This will also delete all its variants.`;
    deleteId.value = socketId;
    deleteType.value = 'socket';

    deleteModal.style.display = 'block';
}

/**
 * Handle deletion of socket or variant
 */
function handleDelete() {
    const deleteModal = document.getElementById('delete-modal');
    const deleteId = document.getElementById('delete-id').value;
    const deleteType = document.getElementById('delete-type').value;

    // Get CSRF token if available
    const csrfTokenElement = document.getElementById('socket-csrf-token');
    let headers = {};

    // Determine URL based on delete type
    let url = deleteType === 'socket'
        ? `/api/sockets.php?action=delete&id=${deleteId}`
        : `/api/sockets.php?action=delete_variant&id=${deleteId}`;

    if (csrfTokenElement) {
        // Add CSRF token as a single header
        // Browsers normalize header names, so we only need to set it once
        headers['X-CSRF-Token'] = csrfTokenElement.value;

        // Also add as URL parameter as fallback
        url += `&csrf_token=${encodeURIComponent(csrfTokenElement.value)}`;
    } else {
        console.warn('CSRF token element not found, proceeding without CSRF token');
    }

    // Send delete request
    fetch(url, {
        method: 'DELETE',
        headers: headers,
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            loadSocketData(true); // Force reload socket data
            // Refresh socket tags after socket data is updated
            setTimeout(loadCpuSocketTags, 500);
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            showNotification(data.message, 'error');
        }

        deleteModal.style.display = 'none';
    })
    .catch(error => {
        console.error('Error deleting item:', error);
        showNotification('Error deleting item. Please try again.', 'error');
        deleteModal.style.display = 'none';
    });
}

/**
 * Initialize CPU Details Management functionality
 */
function initCpuDetailsManagement() {
    // Elements
    const cpuDetailsTab = document.getElementById('cpu-details-tab');
    if (!cpuDetailsTab) return; // Exit if CPU details tab doesn't exist

    const generateCpuDetailsBtn = document.getElementById('generate-cpu-details-btn');
    const cpuDetailsSearch = document.getElementById('cpu-details-search');
    const cpuDetailsSearchClear = document.getElementById('cpu-details-search-clear');

    // Ensure CSRF token is available for CPU details tab
    const csrfTokenElement = document.querySelector('#cpu-data-form input[name="csrf_token"]');
    if (csrfTokenElement && !document.getElementById('cpu-details-csrf-token')) {
        // Create a hidden input for the CSRF token if it doesn't exist
        const csrfTokenInput = document.createElement('input');
        csrfTokenInput.type = 'hidden';
        csrfTokenInput.id = 'cpu-details-csrf-token';
        csrfTokenInput.name = 'csrf_token';
        csrfTokenInput.value = csrfTokenElement.value;
        cpuDetailsTab.appendChild(csrfTokenInput);
    }

    // Event Listeners
    if (generateCpuDetailsBtn) {
        // Remove the generate button click handler since we're not using automatic generation anymore
        generateCpuDetailsBtn.style.display = 'none';
    }

    if (cpuDetailsSearch) {
        cpuDetailsSearch.addEventListener('input', filterCpuDetails);
    }

    if (cpuDetailsSearchClear) {
        cpuDetailsSearchClear.addEventListener('click', () => {
            cpuDetailsSearch.value = '';
            filterCpuDetails();
        });
    }

    // Initialize identifiers popup
    const identifiersPopup = document.getElementById('identifiers-popup');
    const identifiersPopupClose = document.querySelector('.identifiers-popup-close');

    if (identifiersPopupClose) {
        identifiersPopupClose.addEventListener('click', function() {
            closeIdentifiersPopup();
        });
    }



    // Close popup with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && identifiersPopup && identifiersPopup.classList.contains('active')) {
            closeIdentifiersPopup();
        }
    });

    // Initialize Generate All Identifiers button
    const generateAllIdentifiersBtn = document.getElementById('generate-all-identifiers-btn');
    if (generateAllIdentifiersBtn) {
        generateAllIdentifiersBtn.addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Generating...';

            console.log('Generating identifiers for all CPUs');

            // Call API to generate all identifiers
            fetch('/api/cpu-identifiers.php?action=generate&all=true', {
                method: 'GET',
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log(`Successfully generated identifiers for ${data.count} CPU models`);
                    showNotification(`Generated identifiers for ${data.count} CPU models`, 'success');

                    // Clear the cache since all identifiers have been regenerated
                    window.cpuIdentifiersCache = {};

                    // Reload CPU details to refresh the list
                    // Note: Identifiers will be loaded on-demand when popups are opened
                    loadCpuDetails();
                } else {
                    console.error('Failed to generate all identifiers:', data.message);
                    showNotification(data.message || 'Failed to generate CPU identifiers', 'error');
                }

                // Reset button
                this.disabled = false;
                this.textContent = 'Generate All Identifiers';
            })
            .catch(error => {
                console.error('Error generating all CPU identifiers:', error);
                showNotification('Error generating all CPU identifiers: ' + error.message, 'error');

                // Reset button
                this.disabled = false;
                this.textContent = 'Generate All Identifiers';
            });
        });
    }

    // Add event listeners for sortable headers
    const sortableHeaders = cpuDetailsTab.querySelectorAll('th.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', () => {
            const sortBy = header.getAttribute('data-sort');
            sortCpuDetails(sortBy);
        });
    });

    // Initialize CPU details if the tab is active
    if (cpuDetailsTab.classList.contains('active') && !cpuDetailsLoaded) {
        loadCpuDetails();
    }
}

/**
 * Load CPU details from the API
 */
function loadCpuDetails() {
    const cpuDetailsList = document.getElementById('cpu-details-list');
    if (!cpuDetailsList) {
        return;
    }

    cpuDetailsList.innerHTML = '<tr class="loading-row"><td colspan="7">Loading CPU details...</td></tr>';

    // Simple fetch with timestamp to prevent caching
    const url = '/api/cpu-details.php?' + new Date().getTime();

    fetch(url, {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        console.log('CPU details API response:', data);
        if (data.success && data.data && Array.isArray(data.data)) {
            // Store the CPU details data globally
            window.cpuDetailsData = data.data;

            // Initialize identifiers cache if not exists
            if (!window.cpuIdentifiersCache) {
                window.cpuIdentifiersCache = {};
            }

            // Display CPU details
            displayCpuDetails(data.data);
            cpuDetailsLoaded = true;
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            let message = data.message || 'No CPU details found. Please generate CPU details first.';
            cpuDetailsList.innerHTML = `<tr class="no-data-row"><td colspan="7">${message}</td></tr>`;

            if (data.message) {
                showNotification(data.message, 'info');
            }

            // If there's debug output, log it to console
            if (data.debug_output) {
                console.error('Debug output:', data.debug_output);
            }
        }
    })
    .catch(error => {
        console.error('Error fetching CPU details:', error);
        cpuDetailsList.innerHTML = `<tr class="no-data-row"><td colspan="7">Error loading CPU details: ${error.message}</td></tr>`;
        showNotification('Error loading CPU details: ' + error.message, 'error');
    });
}

// Global variable to track current sort state
window.cpuDetailsSortState = {
    column: 'name', // Default sort column
    direction: 'asc' // Default sort direction
};

/**
 * Display CPU details in the table with inline editing
 * @param {Array} cpuDetails - Array of CPU detail objects
 * @param {boolean} applySorting - Whether to apply current sorting (default: true)
 */
function displayCpuDetails(cpuDetails, applySorting = true) {
    const cpuDetailsList = document.getElementById('cpu-details-list');
    if (!cpuDetailsList) return;

    if (!cpuDetails || cpuDetails.length === 0) {
        cpuDetailsList.innerHTML = '<tr class="no-data-row"><td colspan="7">No CPU details found. Please import CPU data first.</td></tr>';
        return;
    }

    // Store the CPU details data in a global variable for filtering and sorting
    window.cpuDetailsData = cpuDetails;

    // Apply current sorting if requested
    if (applySorting && window.cpuDetailsSortState) {
        sortCpuDetails(window.cpuDetailsSortState.column, false);
        return; // sortCpuDetails will call displayCpuDetails again with applySorting=false
    }

    // Create HTML for CPU details with editable fields
    let html = '';
    cpuDetails.forEach(cpu => {
        // Main row HTML
        html += `
            <tr data-id="${cpu.id || ''}" data-original-id="${cpu.original_id || ''}" class="cpu-details-row">
                <td>${cpu.id || ''}</td>
                <td>${cpu.original_id || ''}</td>
                <td>${cpu.name || ''}</td>
                <td class="clickable-field" title="Click to edit">
                    <div class="editable-field">
                        <span class="display-value">${cpu.brand_tag || ''}</span>
                        <input type="text" class="edit-input brand-tag-input" value="${cpu.brand_tag || ''}" style="display: none;">
                    </div>
                </td>
                <td class="clickable-field" title="Click to edit">
                    <div class="editable-field">
                        <span class="display-value">${cpu.series_tag || ''}</span>
                        <input type="text" class="edit-input series-tag-input" value="${cpu.series_tag || ''}" style="display: none;">
                    </div>
                </td>
                <td class="clickable-field" title="Click to edit">
                    <div class="editable-field">
                        <span class="display-value">${cpu.core_model_tag || ''}</span>
                        <input type="text" class="edit-input core-model-tag-input" value="${cpu.core_model_tag || ''}" style="display: none;">
                    </div>
                </td>
                <td class="actions-cell">
                    <button class="icon-button view-identifiers-btn" title="View CPU Details" data-cpu-id="${cpu.id || ''}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                    </button>
                </td>
            </tr>
        `;
    });

    cpuDetailsList.innerHTML = html;

    // Add event listeners for edit buttons
    addCpuDetailsEditListeners();

    // Update sort indicators in the table headers
    updateSortIndicators();

    // Add event listeners for generate identifiers buttons
    addGenerateIdentifiersListeners();
}

/**
 * Render CPU identifiers in a compact format
 *
 * @param {Object} identifiers - Object with simple and compound_and arrays
 * @param {string} cpuId - CPU ID
 * @param {Object} cpu - CPU object with name and other details
 * @return {string} HTML for identifiers
 */
function renderIdentifiers(identifiers, cpuId, cpu = null) {
    if (!identifiers) {
        return `
            <div class="identifiers-loading">
                <p>Loading identifiers...</p>
                <button class="generate-identifiers-btn" data-cpu-id="${cpuId}">Generate Identifiers</button>
            </div>
        `;
    }

    let html = '<div class="identifiers-content">';

    // CPU info section
    if (cpu) {
        html += `<div class="cpu-info-section">`;

        // Always show name
        html += `<div class="cpu-info-row">
            <strong>Name:</strong> ${cpu.name || 'N/A'}
        </div>`;

        // Only show available data for brand, series, model
        let infoHtml = '';

        if (cpu.brand_tag) {
            infoHtml += `<strong>Brand:</strong> ${cpu.brand_tag}`;
        }

        if (cpu.series_tag) {
            if (infoHtml) infoHtml += ' &nbsp;|&nbsp; ';
            infoHtml += `<strong>Series:</strong> ${cpu.series_tag}`;
        }

        if (cpu.core_model_tag) {
            if (infoHtml) infoHtml += ' &nbsp;|&nbsp; ';
            infoHtml += `<strong>Model:</strong> ${cpu.core_model_tag}`;
        }

        if (infoHtml) {
            html += `<div class="cpu-info-row">${infoHtml}</div>`;
        }

        // Add CPU count and mark information
        html += `<div class="cpu-info-row">
            <strong>CPU Count:</strong> ${cpu.cpu_count || '1'} &nbsp;|&nbsp;
            <strong>Mark Score:</strong> ${cpu.mark ? cpu.mark.toString() : '0'}
        </div>`;

        html += `</div>`;
    }

    // Simple identifiers
    if (identifiers.simple && identifiers.simple.length > 0) {
        html += '<div class="identifiers-section">';
        html += '<h4>Simple Identifiers</h4>';
        html += '<div class="identifiers-tags">';

        identifiers.simple.forEach(identifier => {
            html += `<span class="identifier-tag simple-identifier">${identifier}</span>`;
        });

        html += '</div></div>';
    }
    // Removed the "No simple identifiers found" message

    // Compound identifiers
    if (identifiers.compound_and && identifiers.compound_and.length > 0) {
        html += '<div class="identifiers-section">';
        html += '<h4>Compound Identifiers</h4>';
        html += '<div class="identifiers-tags">';

        identifiers.compound_and.forEach(parts => {
            if (Array.isArray(parts) && parts.length === 2) {
                html += `<span class="identifier-tag compound-identifier">${parts[0]} + ${parts[1]}</span>`;
            }
        });

        html += '</div></div>';
    }
    // Removed the "No compound identifiers found" message

    // Add regenerate button
    html += `
        <div class="identifiers-actions">
            <button class="regenerate-identifiers-btn" data-cpu-id="${cpuId}">Regenerate Identifiers</button>
        </div>
    `;

    html += '</div>';
    return html;
}

// Flag to track if a popup is currently being opened
let isPopupOpening = false;

/**
 * Open the identifiers popup for a specific CPU
 * Loads identifiers only when the popup is opened
 *
 * @param {string} cpuId - CPU ID
 */
function openIdentifiersPopup(cpuId) {
    // Prevent multiple rapid openings
    if (isPopupOpening) {
        console.log('Popup already opening, ignoring request');
        return;
    }

    isPopupOpening = true;

    // Set a timeout to reset the flag after a short delay
    setTimeout(() => {
        isPopupOpening = false;
    }, 500); // 500ms debounce

    const popup = document.getElementById('identifiers-popup');
    const container = document.getElementById('identifiers-popup-container');
    const titleElement = document.querySelector('.identifiers-popup-title');

    if (!popup || !container) {
        isPopupOpening = false;
        return;
    }

    // Show loading state
    container.innerHTML = '<div class="identifiers-loading"><p>Loading identifiers...</p></div>';

    // Find CPU data
    const cpu = window.cpuDetailsData?.find(c => c.id == cpuId);

    // Update title
    if (titleElement && cpu) {
        titleElement.textContent = `CPU Identifiers: ${cpu.name || 'CPU #' + cpuId}`;
    }

    // Show popup
    popup.classList.add('active');

    // Check if we already have identifiers in cache
    if (window.cpuIdentifiersCache && window.cpuIdentifiersCache[cpuId]) {
        console.log(`Using cached identifiers for CPU ${cpuId}`);
        container.innerHTML = renderIdentifiers(window.cpuIdentifiersCache[cpuId], cpuId, cpu);
        addGenerateIdentifiersListeners();
        isPopupOpening = false;
        return;
    }

    console.log(`Fetching identifiers for CPU ${cpuId}`);

    // Load identifiers for this specific CPU only
    fetch(`/api/cpu-identifiers.php?id=${cpuId}`, {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            console.log(`Successfully loaded identifiers for CPU ${cpuId}`);

            // Update cache
            if (!window.cpuIdentifiersCache) {
                window.cpuIdentifiersCache = {};
            }
            window.cpuIdentifiersCache[cpuId] = data.data;

            // Update display
            container.innerHTML = renderIdentifiers(data.data, cpuId, cpu);

            // Add event listeners
            addGenerateIdentifiersListeners();
        } else {
            console.log(`No identifiers found for CPU ${cpuId}`);

            // Show generate button if no identifiers found
            container.innerHTML = renderIdentifiers(null, cpuId, cpu);
            addGenerateIdentifiersListeners();
        }
        isPopupOpening = false;
    })
    .catch(error => {
        console.error(`Error loading identifiers for CPU ${cpuId}:`, error);
        container.innerHTML = `
            <div class="error-message">
                <p>Error loading identifiers: ${error.message}</p>
                <button class="generate-identifiers-btn" data-cpu-id="${cpuId}">Generate Identifiers</button>
            </div>
        `;
        addGenerateIdentifiersListeners();
        isPopupOpening = false;
    });
}

/**
 * Close the identifiers popup
 */
function closeIdentifiersPopup() {
    const popup = document.getElementById('identifiers-popup');
    if (popup) {
        popup.classList.remove('active');
    }
}

// Store references to event handler functions to allow proper removal
const viewButtonHandler = function(e) {
    e.preventDefault();
    const cpuId = this.getAttribute('data-cpu-id');
    if (cpuId) {
        openIdentifiersPopup(cpuId);
    }
};

const generateButtonHandler = function(e) {
    e.preventDefault();
    const cpuId = this.getAttribute('data-cpu-id');
    if (!cpuId) return;

    // Prevent multiple clicks
    if (this.disabled) return;

    // Show loading state
    this.textContent = 'Generating...';
    this.disabled = true;

    console.log(`Generating identifiers for CPU ${cpuId}`);

    // Call API to generate identifiers
    fetch(`/api/cpu-identifiers.php?action=generate&id=${cpuId}`, {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            console.log(`Successfully generated identifiers for CPU ${cpuId}`);

            // Update cache
            if (!window.cpuIdentifiersCache) {
                window.cpuIdentifiersCache = {};
            }
            window.cpuIdentifiersCache[cpuId] = data.data;

            // Find CPU data
            const cpu = window.cpuDetailsData?.find(c => c.id == cpuId);

            // Update popup content
            const container = document.getElementById('identifiers-popup-container');
            if (container) {
                container.innerHTML = renderIdentifiers(data.data, cpuId, cpu);
                addGenerateIdentifiersListeners();
            }

            showNotification('CPU identifiers generated successfully', 'success');
        } else {
            console.error(`Failed to generate identifiers for CPU ${cpuId}:`, data.message);
            showNotification(data.message || 'Failed to generate CPU identifiers', 'error');

            // Reset button
            this.textContent = 'Generate Identifiers';
            this.disabled = false;
        }
    })
    .catch(error => {
        console.error(`Error generating identifiers for CPU ${cpuId}:`, error);
        showNotification('Error generating CPU identifiers: ' + error.message, 'error');

        // Reset button
        this.textContent = 'Generate Identifiers';
        this.disabled = false;
    });
};

/**
 * Add event listeners for generate identifiers buttons
 * Ensures each button has only one event listener
 */
function addGenerateIdentifiersListeners() {
    // First, remove any existing event listeners from view buttons
    const viewButtons = document.querySelectorAll('.view-identifiers-btn');
    viewButtons.forEach(button => {
        // Clone the button to remove all event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Add the event listener to the new button
        newButton.addEventListener('click', viewButtonHandler);
    });

    // Remove existing event listeners from generate buttons
    const generateButtons = document.querySelectorAll('.generate-identifiers-btn, .regenerate-identifiers-btn');
    generateButtons.forEach(button => {
        // Clone the button to remove all event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Add the event listener to the new button
        newButton.addEventListener('click', generateButtonHandler);
    });
}

/**
 * Sort CPU details by the specified column
 * @param {string} column - Column to sort by (id, original_id, name)
 * @param {boolean} toggleDirection - Whether to toggle the sort direction (default: true)
 */
function sortCpuDetails(column, toggleDirection = true) {
    if (!window.cpuDetailsData || !Array.isArray(window.cpuDetailsData)) {
        return;
    }

    // If sorting by the same column, toggle direction
    if (toggleDirection && window.cpuDetailsSortState.column === column) {
        window.cpuDetailsSortState.direction = window.cpuDetailsSortState.direction === 'asc' ? 'desc' : 'asc';
    } else {
        // If sorting by a new column, set it and default to ascending
        window.cpuDetailsSortState.column = column;
        if (toggleDirection) {
            window.cpuDetailsSortState.direction = 'asc';
        }
    }

    // Create a copy of the data to sort
    const sortedData = [...window.cpuDetailsData];

    // Sort the data based on the selected column and direction
    sortedData.sort((a, b) => {
        let valueA = a[column] || '';
        let valueB = b[column] || '';

        // For numeric columns, convert to numbers for proper numeric sorting
        if (column === 'id' || column === 'original_id' || column === 'cpu_count' || column === 'mark') {
            valueA = valueA ? parseInt(valueA, 10) : 0;
            valueB = valueB ? parseInt(valueB, 10) : 0;
        } else {
            // For text columns, convert to lowercase for case-insensitive sorting
            valueA = valueA.toString().toLowerCase();
            valueB = valueB.toString().toLowerCase();
        }

        // Compare values based on sort direction
        if (window.cpuDetailsSortState.direction === 'asc') {
            return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
        } else {
            return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
        }
    });

    // Display the sorted data without reapplying sorting
    displayCpuDetails(sortedData, false);
}

/**
 * Update sort indicators in the table headers
 */
function updateSortIndicators() {
    // Remove all sort indicators
    const sortableHeaders = document.querySelectorAll('th.sortable');
    sortableHeaders.forEach(header => {
        header.classList.remove('sort-asc', 'sort-desc');
    });

    // Add the appropriate sort indicator to the current sort column
    const currentSortHeader = document.querySelector(`th.sortable[data-sort="${window.cpuDetailsSortState.column}"]`);
    if (currentSortHeader) {
        currentSortHeader.classList.add(window.cpuDetailsSortState.direction === 'asc' ? 'sort-asc' : 'sort-desc');
    }
}

/**
 * Filter CPU details based on search input
 */
function filterCpuDetails() {
    const searchInput = document.getElementById('cpu-details-search');
    if (!searchInput) return;

    const searchTerm = searchInput.value.toLowerCase();

    if (!window.cpuDetailsData || !Array.isArray(window.cpuDetailsData)) {
        return;
    }

    // Filter CPU details based on search term
    const filteredData = window.cpuDetailsData.filter(cpu => {
        return (
            (cpu.id && cpu.id.toString().toLowerCase().includes(searchTerm)) ||
            (cpu.original_id && cpu.original_id.toString().toLowerCase().includes(searchTerm)) ||
            (cpu.name && cpu.name.toLowerCase().includes(searchTerm)) ||
            (cpu.brand_tag && cpu.brand_tag.toLowerCase().includes(searchTerm)) ||
            (cpu.series_tag && cpu.series_tag.toLowerCase().includes(searchTerm)) ||
            (cpu.core_model_tag && cpu.core_model_tag.toLowerCase().includes(searchTerm))
        );
    });

    // Display filtered data with current sorting
    displayCpuDetails(filteredData, true);
}

/**
 * Add event listeners for CPU details edit buttons and clickable fields
 */
function addCpuDetailsEditListeners() {
    // Track the currently active edit row
    window.activeEditRow = null;

    // Clickable fields (brand, series, model)
    const clickableFields = document.querySelectorAll('.clickable-field');
    clickableFields.forEach(field => {
        field.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent the document click handler from firing
            const row = this.closest('tr');

            // Only enable edit mode if the row is not already in edit mode
            if (!row.classList.contains('active-edit')) {
                // If there's already an active edit row, save its changes first
                if (window.activeEditRow && window.activeEditRow !== row) {
                    saveCpuDetails(window.activeEditRow);
                }

                // Enable edit mode for this row
                enableEditMode(row);
                window.activeEditRow = row;

                // Determine which field was clicked (brand, series, or model)
                let fieldType;
                if (this.tagName === 'TD') {
                    // If the td was clicked, find the input inside it
                    const input = this.querySelector('.edit-input');
                    if (input) {
                        fieldType = input.className.split(' ')[1];
                    }
                } else {
                    // If the span was clicked (legacy support)
                    const input = this.parentElement.querySelector('.edit-input');
                    if (input) {
                        fieldType = input.className.split(' ')[1];
                    }
                }

                // Focus the input field
                if (fieldType) {
                    const input = row.querySelector('.' + fieldType);
                    if (input) {
                        input.focus();
                        // Position cursor at the end of the text
                        const length = input.value.length;
                        input.setSelectionRange(length, length);
                    }
                }
            }
        });
    });

    // Add keyboard event listeners to input fields
    const inputFields = document.querySelectorAll('.edit-input');
    inputFields.forEach(input => {
        // Enter key to save and move to next row
        input.addEventListener('keydown', function(e) {
            const row = this.closest('tr');

            if (e.key === 'Enter') {
                e.preventDefault();

                // Save current row
                saveCpuDetails(row);

                // Find the next row
                const nextRow = row.nextElementSibling;
                if (nextRow) {
                    // Get the class name of the current input to determine which column we're in
                    const inputClass = this.className.split(' ')[1]; // e.g., "brand-tag-input"

                    // Wait a tiny bit for the current row to finish saving
                    setTimeout(() => {
                        // Enable edit mode on the next row
                        enableEditMode(nextRow);

                        // Focus the same column's input in the next row
                        const nextInput = nextRow.querySelector('.' + inputClass);
                        if (nextInput) {
                            nextInput.focus();
                            // Position cursor at the end of the text
                            const length = nextInput.value.length;
                            nextInput.setSelectionRange(length, length);
                        }
                    }, 50);
                }
            } else if (e.key === 'Escape') {
                e.preventDefault();
                disableEditMode(row, true);
                window.activeEditRow = null;
            }
        });
    });

    // Add global document click handler to save changes when clicking outside the row
    document.addEventListener('click', function(e) {
        // If there's an active edit row
        if (window.activeEditRow) {
            // Check if the click was outside the active row
            const clickedRow = e.target.closest('tr');

            // If clicked outside any row or on a different row
            if (!clickedRow || clickedRow !== window.activeEditRow) {
                saveCpuDetails(window.activeEditRow);
            }
        }
    });

    // Add global keyboard event listeners
    document.addEventListener('keydown', function(e) {
        if (!window.activeEditRow) return;

        if (e.key === 'Enter') {
            saveCpuDetails(window.activeEditRow);
            e.preventDefault();
        } else if (e.key === 'Escape') {
            disableEditMode(window.activeEditRow, true);
            window.activeEditRow = null;
            e.preventDefault();
        }
    });
}

/**
 * Enable edit mode for a CPU details row
 * @param {HTMLElement} row - The table row to enable edit mode for
 */
function enableEditMode(row) {
    // If there's already an active edit row that's different from this one, disable it first
    if (window.activeEditRow && window.activeEditRow !== row) {
        disableEditMode(window.activeEditRow, false);
    }

    // Hide display values and show input fields
    const displayValues = row.querySelectorAll('.display-value');
    const inputFields = row.querySelectorAll('.edit-input');

    displayValues.forEach(display => {
        display.style.display = 'none';
    });

    inputFields.forEach(input => {
        input.style.display = 'block';
        input.style.position = 'relative'; // Ensure proper positioning
    });

    // Add active-edit class to the row
    row.classList.add('active-edit');

    // Set this as the active edit row
    window.activeEditRow = row;
}

/**
 * Disable edit mode for a CPU details row
 * @param {HTMLElement} row - The table row to disable edit mode for
 * @param {boolean} revert - Whether to revert changes
 */
function disableEditMode(row, revert = false) {
    // Get display values and input fields
    const displayValues = row.querySelectorAll('.display-value');
    const inputFields = row.querySelectorAll('.edit-input');

    // If reverting changes, reset input values to display values
    if (revert) {
        displayValues.forEach((display, index) => {
            inputFields[index].value = display.textContent;
        });
    } else {
        // Update display values with input values
        displayValues.forEach((display, index) => {
            display.textContent = inputFields[index].value;
        });
    }

    // Hide input fields and show display values
    displayValues.forEach(display => {
        display.style.display = 'flex';
    });

    inputFields.forEach(input => {
        input.style.display = 'none';
    });

    // Remove active-edit class from the row
    row.classList.remove('active-edit');

    // If this is the active edit row, clear it
    if (window.activeEditRow === row) {
        window.activeEditRow = null;
    }
}

/**
 * Save CPU details for a row
 * @param {HTMLElement} row - The table row to save details for
 */
function saveCpuDetails(row) {
    // If row is not in edit mode, do nothing
    if (!row.classList.contains('active-edit')) {
        return;
    }

    // Get CPU ID
    const cpuId = row.getAttribute('data-id');

    // Get display values (original values)
    const displayValues = row.querySelectorAll('.display-value');
    const originalBrandTag = displayValues[0].textContent;
    const originalSeriesTag = displayValues[1].textContent;
    const originalCoreModelTag = displayValues[2].textContent;

    // Get input values (new values) and trim whitespace
    const brandTag = row.querySelector('.brand-tag-input').value.trim();
    const seriesTag = row.querySelector('.series-tag-input').value.trim();
    const coreModelTag = row.querySelector('.core-model-tag-input').value.trim();

    // Check if any values have changed
    if (brandTag === originalBrandTag &&
        seriesTag === originalSeriesTag &&
        coreModelTag === originalCoreModelTag) {
        // No changes, just exit edit mode without saving
        disableEditMode(row, false);
        return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('action', 'update');
    formData.append('id', cpuId);
    formData.append('brand_tag', brandTag);
    formData.append('series_tag', seriesTag);
    formData.append('core_model_tag', coreModelTag);

    // Show loading indicator by adding a class to the row
    row.classList.add('saving');

    // Send request to update CPU details
    fetch('/api/cpu-details.php', {
        method: 'POST',
        body: formData,
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        // Remove loading indicator
        row.classList.remove('saving');

        if (data.success) {
            // Disable edit mode without reverting changes
            disableEditMode(row, false);

            // Show success notification
            showNotification(data.message || 'CPU details updated successfully', 'success');

            // Update the data in the global variable
            if (window.cpuDetailsData) {
                const cpuIndex = window.cpuDetailsData.findIndex(cpu => cpu.id == cpuId);
                if (cpuIndex !== -1) {
                    window.cpuDetailsData[cpuIndex].brand_tag = brandTag;
                    window.cpuDetailsData[cpuIndex].series_tag = seriesTag;
                    window.cpuDetailsData[cpuIndex].core_model_tag = coreModelTag;

                    // If we're sorting by a field that was just updated, re-sort the data
                    if (window.cpuDetailsSortState &&
                        (window.cpuDetailsSortState.column === 'brand_tag' ||
                         window.cpuDetailsSortState.column === 'series_tag' ||
                         window.cpuDetailsSortState.column === 'core_model_tag')) {
                        sortCpuDetails(window.cpuDetailsSortState.column, false);
                    }
                }
            }

            // Automatically regenerate identifiers for this CPU
            regenerateIdentifiersForCpu(cpuId);
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            // Show error notification
            showNotification(data.message || 'Error updating CPU details', 'error');

            // Keep edit mode active if there was an error
            // This allows the user to correct the issue
        }
    })
    .catch(error => {
        // Remove loading indicator
        row.classList.remove('saving');

        console.error('Error updating CPU details:', error);
        showNotification('Error updating CPU details: ' + error.message, 'error');
    });
}

/**
 * Regenerate identifiers for all CPUs
 */
function regenerateAllIdentifiers() {
    console.log('Regenerating identifiers for all CPUs');

    // Call API to generate identifiers for all CPUs
    fetch('/api/cpu-identifiers.php?action=generate&all=true', {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`Successfully regenerated identifiers for ${data.count} CPUs`);

            // Clear the cache since all identifiers have been regenerated
            window.cpuIdentifiersCache = {};
        } else {
            console.error('Failed to regenerate all identifiers:', data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error regenerating all identifiers:', error);
    });
}

/**
 * Regenerate identifiers for a specific CPU
 *
 * @param {string} cpuId - The ID of the CPU to regenerate identifiers for
 */
function regenerateIdentifiersForCpu(cpuId) {
    if (!cpuId) return;

    console.log('Regenerating identifiers for CPU ID:', cpuId);

    // Call API to generate identifiers
    fetch(`/api/cpu-identifiers.php?action=generate&id=${cpuId}`, {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            // Update cache
            if (!window.cpuIdentifiersCache) {
                window.cpuIdentifiersCache = {};
            }
            window.cpuIdentifiersCache[cpuId] = data.data;

            console.log('Identifiers regenerated successfully for CPU ID:', cpuId);
        } else {
            console.error('Failed to regenerate identifiers for CPU ID:', cpuId, data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error regenerating identifiers for CPU ID:', cpuId, error);
    });
}

/**
 * Show notification
 *
 * @param {string} message Notification message
 * @param {string} type Notification type ('success' or 'error')
 */
function showNotification(message, type = 'success') {
    // Get or create notification element
    let notification = document.getElementById('notification');

    // If notification doesn't exist, create it
    if (!notification) {
        notification = document.createElement('div');
        notification.id = 'notification';

        const notificationMessage = document.createElement('span');
        notificationMessage.id = 'notification-message';

        const closeButton = document.createElement('span');
        closeButton.className = 'notification-close';
        closeButton.innerHTML = '&times;';
        closeButton.addEventListener('click', () => {
            notification.style.display = 'none';
        });

        notification.appendChild(notificationMessage);
        notification.appendChild(closeButton);

        // Add to body to ensure it's always visible regardless of tab
        document.body.appendChild(notification);
    }

    // Get notification message element
    const notificationMessage = document.getElementById('notification-message');

    // Reset and set classes
    notification.className = 'notification';
    notification.classList.add(type);

    // Set message
    notificationMessage.textContent = message;

    // Show notification
    notification.style.display = 'block';

    // Reset any existing timeout
    if (notification.timeoutId) {
        clearTimeout(notification.timeoutId);
    }

    // Hide notification after 5 seconds
    notification.timeoutId = setTimeout(() => {
        notification.style.display = 'none';
    }, 5000);
}

/**
 * Handle unauthorized access by redirecting to login page
 * This is called when an API response indicates the session has expired
 *
 * @param {string} message Optional message to display before redirecting
 */
function handleUnauthorizedAccess(message = 'Your session has expired. Redirecting to login page...') {
    // Prevent multiple redirects
    if (redirectInProgress) {
        return;
    }

    // Set the flag to prevent further redirects
    redirectInProgress = true;

    // Show notification
    showNotification(message, 'error');

    // Redirect to login page after a short delay
    setTimeout(() => {
        // Clear any stored data that might cause issues
        if (window.cpuDetailsData) {
            window.cpuDetailsData = null;
        }
        if (window.cpuModelsData) {
            window.cpuModelsData = null;
        }

        // Clear any localStorage or sessionStorage data
        try {
            localStorage.clear();
            sessionStorage.clear();
        } catch (e) {
            console.error('Error clearing storage:', e);
        }

        // Redirect to login.php with expired parameter
        window.location.href = '/login.php?expired=1';
    }, 1500);
}

/**
 * Initialize CPU Data Management functionality
 */
function initCpuDataManagement() {
    // Initialize custom rules functionality
    initCustomRules();
    // Elements
    const cpuDataTab = document.getElementById('cpu-data');
    if (!cpuDataTab) return; // Exit if CPU data tab doesn't exist

    const cpuDataForm = document.getElementById('cpu-data-form');
    const cpuDataJson = document.getElementById('cpu-data-json');
    const cpuDataClear = document.getElementById('cpu-data-clear');
    const showSampleJsonLink = document.getElementById('show-sample-json');

    // Event Listeners
    if (cpuDataForm) {
        cpuDataForm.addEventListener('submit', handleCpuDataFormSubmit);
    }

    if (cpuDataClear) {
        cpuDataClear.addEventListener('click', () => {
            cpuDataJson.value = '';

            // Clear any error messages
            const errorContainer = document.getElementById('cpu-data-error');
            if (errorContainer) {
                errorContainer.innerHTML = '';
                errorContainer.style.display = 'none';
            }
        });
    }

    // Sample JSON link
    if (showSampleJsonLink) {
        showSampleJsonLink.addEventListener('click', (e) => {
            e.preventDefault();

            // Clear any error messages
            const errorContainer = document.getElementById('cpu-data-error');
            if (errorContainer) {
                errorContainer.innerHTML = '';
                errorContainer.style.display = 'none';
            }

            const sampleJson = {
                "data": [
                    {
                        "id": "5934",
                        "name": "AArch64",
                        "price": "NA",
                        "cpumark": "833",
                        "thread": "368",
                        "value": "NA",
                        "threadValue": "NA",
                        "tdp": "NA",
                        "powerPerf": "NA",
                        "date": "Mar 2024",
                        "socket": "Unknown",
                        "cat": "Mobile/Embedded",
                        "speed": "1300",
                        "turbo": "NA",
                        "cpuCount": 1,
                        "cores": "4",
                        "logicals": "1",
                        "secondaryCores": "0",
                        "secondaryLogicals": "0",
                        "rank": 4138,
                        "samples": "7",
                        "href": "AArch64&id=5934",
                        "output": true
                    },
                    {
                        "id": "4017",
                        "name": "AArch64 rev 2 (aarch64)",
                        "price": "NA",
                        "cpumark": "2,409",
                        "thread": "908",
                        "value": "NA",
                        "threadValue": "NA",
                        "tdp": "NA",
                        "powerPerf": "NA",
                        "date": "Feb 2021",
                        "socket": "Unknown",
                        "cat": "Mobile/Embedded",
                        "speed": "2189",
                        "turbo": "NA",
                        "cpuCount": 1,
                        "cores": "8",
                        "logicals": "1",
                        "secondaryCores": "0",
                        "secondaryLogicals": "0",
                        "rank": 2917,
                        "samples": "64",
                        "href": "AArch64+rev+2+%28aarch64%29&id=4017",
                        "output": true
                    }
                ]
            };

            cpuDataJson.value = JSON.stringify(sampleJson, null, 2);
        });
    }

    // Initialize CPU models search and filter functionality
    const cpuModelsSearch = document.getElementById('cpu-models-search');
    const cpuModelsSearchClear = document.getElementById('cpu-models-search-clear');
    const socketFilter = document.getElementById('cpu-models-socket-filter');
    const categoryFilter = document.getElementById('cpu-models-category-filter');

    // Search input event
    if (cpuModelsSearch) {
        cpuModelsSearch.addEventListener('input', function() {
            const searchValue = this.value.trim().toLowerCase();

            // Show/hide clear button
            if (cpuModelsSearchClear) {
                cpuModelsSearchClear.style.display = searchValue ? 'block' : 'none';
            }

            // Apply all filters
            applyFilters();
        });
    }

    // Search clear button event
    if (cpuModelsSearchClear) {
        cpuModelsSearchClear.addEventListener('click', function() {
            if (cpuModelsSearch) {
                cpuModelsSearch.value = '';
                cpuModelsSearch.focus();
                this.style.display = 'none';

                // Apply all filters
                applyFilters();
            }
        });
    }

    // Socket filter change event
    if (socketFilter) {
        socketFilter.addEventListener('change', function() {
            applyFilters();
        });
    }

    // Category filter change event
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            applyFilters();
        });
    }

    // Load CPU data statistics and models if tab is active on page load
    if (cpuDataTab.classList.contains('active')) {
        loadCpuDataStats();
        loadCpuModels();

        // Check if the final data tab is active
        const finalDataTab = document.getElementById('final-data-tab');
        if (finalDataTab && finalDataTab.classList.contains('active')) {
            loadFinalData();
        }
    }
}

/**
 * Load CPU data statistics
 * @param {boolean} forceReload - Whether to force reload the data even if it was loaded before
 */
function loadCpuDataStats(forceReload = false) {
    // If data is already loaded and we're not forcing a reload, return
    if (cpuDataStatsLoaded && !forceReload) {
        return;
    }

    const cpuStatsContainer = document.getElementById('cpu-stats-container');
    if (!cpuStatsContainer) return;

    cpuStatsContainer.innerHTML = 'Loading statistics...';

    // Get CSRF token from the form or socket-csrf-token element
    const formCsrfToken = document.querySelector('#cpu-data-form input[name="csrf_token"]');
    const csrfTokenElement = document.getElementById('socket-csrf-token');

    let csrfToken = '';
    if (formCsrfToken && formCsrfToken.value) {
        csrfToken = formCsrfToken.value;
    } else if (csrfTokenElement && csrfTokenElement.value) {
        csrfToken = csrfTokenElement.value;
    }

    let headers = {};
    let url = '/api/cpu-data.php?action=count';

    if (csrfToken) {
        // Add CSRF token as a single header
        headers['X-CSRF-Token'] = csrfToken;

        // Also add as URL parameter as fallback
        url += '&csrf_token=' + encodeURIComponent(csrfToken);
    } else {
        console.warn('CSRF token not found, proceeding without CSRF token');
    }

    // Log request details for debugging
    console.log('CPU Stats Request:', {
        url: url,
        method: 'GET',
        headers: headers
    });

    fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => {
        console.log('CPU Stats Response Status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Log response data for debugging
        console.log('CPU Stats Response Data:', data);

        if (data.success) {
            // Display CPU data statistics as compact text
            cpuStatsContainer.innerHTML = `Total CPU Models: ${data.count || 0}`;

            // Set the flag to indicate that data has been loaded
            cpuDataStatsLoaded = true;
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            cpuStatsContainer.innerHTML = `Error loading statistics: ${data.message}`;
            cpuStatsContainer.classList.add('error-text');
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error fetching CPU data statistics:', error);
        cpuStatsContainer.innerHTML = `Error loading statistics. Please try again.`;
        cpuStatsContainer.classList.add('error-text');
        showNotification('Error loading CPU data statistics. Please try again.', 'error');
    });
}

/**
 * Handle CPU data form submission
 *
 * @param {Event} e Form submit event
 */
function handleCpuDataFormSubmit(e) {
    e.preventDefault();

    const cpuDataJson = document.getElementById('cpu-data-json');
    const jsonContent = cpuDataJson.value.trim();

    if (!jsonContent) {
        showNotification('Please enter JSON data.', 'error');
        return;
    }

    // Try to parse JSON to validate format
    try {
        JSON.parse(jsonContent);
    } catch (error) {
        showNotification('Invalid JSON format: ' + error.message, 'error');
        return;
    }

    // Get CSRF token from the form or socket-csrf-token element
    const formCsrfToken = document.querySelector('#cpu-data-form input[name="csrf_token"]');
    const csrfTokenElement = document.getElementById('socket-csrf-token');

    let csrfToken = '';
    if (formCsrfToken && formCsrfToken.value) {
        csrfToken = formCsrfToken.value;
    } else if (csrfTokenElement && csrfTokenElement.value) {
        csrfToken = csrfTokenElement.value;
    }

    let headers = {
        'Content-Type': 'application/json'
    };
    let url = '/api/cpu-data.php?action=update';

    if (csrfToken) {
        // Add CSRF token as a single header
        headers['X-CSRF-Token'] = csrfToken;

        // Also add as URL parameter as fallback
        url += '&csrf_token=' + encodeURIComponent(csrfToken);
    } else {
        console.warn('CSRF token not found, proceeding without CSRF token');
    }

    // Show loading state
    const submitButton = document.getElementById('cpu-data-submit');
    const originalButtonText = submitButton.textContent;
    submitButton.textContent = 'Updating...';
    submitButton.disabled = true;

    // Log request details for debugging
    console.log('CPU Data Update Request:', {
        url: url,
        method: 'POST',
        headers: headers,
        body: jsonContent.substring(0, 100) + '...' // Log just the beginning for privacy
    });

    // Send request
    fetch(url, {
        method: 'POST',
        headers: headers,
        body: jsonContent,
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => {
        console.log('CPU Data Update Response Status:', response.status);
        console.log('CPU Data Update Response Headers:', response.headers.get('content-type'));

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            // If it's not JSON, get the text to see what was returned
            return response.text().then(text => {
                console.error('Non-JSON response received:', text.substring(0, 500));
                throw new Error('Server returned non-JSON response. This usually means you are not logged in or your session has expired.');
            });
        }

        return response.json();
    })
    .then(data => {
        // Log response data for debugging
        console.log('CPU Data Update Response Data:', data);

        // Reset button state
        submitButton.textContent = originalButtonText;
        submitButton.disabled = false;

        if (data.success) {
            showNotification(data.message, 'success');

            // Clear form if successful
            cpuDataJson.value = '';

            // Reload statistics (force reload)
            loadCpuDataStats(true);

            // Reset the CPU models loaded flag so it will be reloaded when the review tab is opened
            cpuModelsLoaded = false;

            // If the review tab is currently active, reload the CPU models with a slight delay
            const reviewTab = document.getElementById('review-tab');
            if (reviewTab && reviewTab.classList.contains('active')) {
                // Reload CPU models
                loadCpuModels(true)
            }

            // Generate whitelisted data automatically
            generateFinalData();

            // Regenerate all identifiers after import
            regenerateAllIdentifiers();
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            // Show error notification
            showNotification(data.message, 'error');

            // Display detailed error in the form
            const errorContainer = document.getElementById('cpu-data-error');
            if (errorContainer) {
                errorContainer.innerHTML = `
                    <div class="error-container">
                        <p>${data.message}</p>
                        ${data.debug_output ? `
                            <details>
                                <summary>Show Debug Info</summary>
                                <pre>${data.debug_output}</pre>
                            </details>
                        ` : ''}
                    </div>
                `;
                errorContainer.style.display = 'block';
            }
        }
    })
    .catch(error => {
        // Reset button state
        submitButton.textContent = originalButtonText;
        submitButton.disabled = false;

        console.error('Error updating CPU data:', error);

        // Check if this is a session-related error
        if (error.message && error.message.includes('session has expired')) {
            showNotification('Your session has expired. Please refresh the page and log in again.', 'error');
            // Optionally redirect to login or refresh the page
            setTimeout(() => {
                window.location.reload();
            }, 3000);
            return;
        }

        showNotification('Error updating CPU data: ' + (error.message || 'Unknown error'), 'error');

        // Display error in the form
        const errorContainer = document.getElementById('cpu-data-error');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="error-container">
                    <p>Error updating CPU data: ${error.message || 'Unknown error'}</p>
                    <details>
                        <summary>Show Error Details</summary>
                        <pre>${error.stack || error.toString()}</pre>
                    </details>
                </div>
            `;
            errorContainer.style.display = 'block';
        }
    });
}

/**
 * Load CPU models data
 * @param {boolean} forceReload - Whether to force reload the data even if it was loaded before
 */
function loadCpuModels(forceReload = false) {
    const cpuModelsList = document.getElementById('cpu-models-list');
    if (!cpuModelsList) return;

    // Check if the review tab is active
    const reviewTab = document.getElementById('review-tab');
    if (reviewTab && !reviewTab.classList.contains('active')) {
        // If the review tab is not active, don't load the data yet
        // It will be loaded when the tab is activated
        return;
    }

    // If data is already loaded and we're not forcing a reload, return
    if (cpuModelsLoaded && !forceReload) {
        return;
    }

    // Show loading state
    cpuModelsList.innerHTML = '<tr class="loading-row"><td colspan="9">Loading CPU models...</td></tr>';

    // Get CSRF token from the form or socket-csrf-token element
    const formCsrfToken = document.querySelector('#cpu-data-form input[name="csrf_token"]');
    const csrfTokenElement = document.getElementById('socket-csrf-token');

    let csrfToken = '';
    if (formCsrfToken && formCsrfToken.value) {
        csrfToken = formCsrfToken.value;
    } else if (csrfTokenElement && csrfTokenElement.value) {
        csrfToken = csrfTokenElement.value;
    }

    let headers = {};
    let url = '/api/cpu-data.php?action=list';

    if (csrfToken) {
        // Add CSRF token as a single header
        headers['X-CSRF-Token'] = csrfToken;

        // Also add as URL parameter as fallback
        url += '&csrf_token=' + encodeURIComponent(csrfToken);
    } else {
        console.warn('CSRF token not found, proceeding without CSRF token');
    }

    // Log request details for debugging
    console.log('CPU Models Request:', {
        url: url,
        method: 'GET',
        headers: headers
    });

    fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => {
        console.log('CPU Models Response Status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Log response data for debugging
        console.log('CPU Models Response Data:', data);

        if (data.success && data.data && Array.isArray(data.data)) {
            // Store the CPU models data in a global variable for filtering
            window.cpuModelsData = data.data;

            // Populate filter dropdowns
            populateFilterDropdowns(data.data);

            // Display CPU models
            displayCpuModels(data.data);

            // Set the flag to indicate that CPU models have been loaded
            cpuModelsLoaded = true;
        } else {
            // Check if this is an unauthorized access error (session expired)
            if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                handleUnauthorizedAccess();
                return;
            }

            cpuModelsList.innerHTML = `<tr class="no-data-row"><td colspan="9">Error: ${data.message || 'Failed to load CPU models'}</td></tr>`;
            showNotification(data.message || 'Failed to load CPU models', 'error');
        }
    })
    .catch(error => {
        console.error('Error fetching CPU models:', error);
        cpuModelsList.innerHTML = `<tr class="no-data-row"><td colspan="9">Error loading CPU models. Please try again.</td></tr>`;
        showNotification('Error loading CPU models. Please try again.', 'error');
    });
}

/**
 * Display CPU models in the table
 *
 * @param {Array} models Array of CPU model objects
 */
function displayCpuModels(models) {
    const cpuModelsList = document.getElementById('cpu-models-list');
    if (!cpuModelsList) return;

    if (!models || models.length === 0) {
        cpuModelsList.innerHTML = '<tr class="no-data-row"><td colspan="9">No CPU models found.</td></tr>';
        return;
    }

    // Clear the table
    cpuModelsList.innerHTML = '';

    // Add each CPU model to the table
    models.forEach(model => {
        const row = document.createElement('tr');
        row.className = 'cpu-model-row';
        row.setAttribute('data-id', model.id);
        row.setAttribute('data-name', model.name);
        row.setAttribute('data-socket', model.socket ? model.socket.trim() : model.socket);
        row.setAttribute('data-category', model.cat);

        // Format the row content
        row.innerHTML = `
            <td>${model.id}</td>
            <td>${model.name}</td>
            <td>${model.cpumark}</td>
            <td>${model.tdp}</td>
            <td>${model.socket ? model.socket.trim() : model.socket}</td>
            <td>${model.cat}</td>
            <td>${model.cpuCount}</td>
            <td>${model.cores}</td>
            <td>${model.secondaryCores}</td>
        `;

        cpuModelsList.appendChild(row);
    });
}

/**
 * Populate filter dropdowns with unique values from the data
 *
 * @param {Array} models Array of CPU model objects
 */
function populateFilterDropdowns(models) {
    const socketFilter = document.getElementById('cpu-models-socket-filter');
    const categoryFilter = document.getElementById('cpu-models-category-filter');

    if (!socketFilter || !categoryFilter) return;

    // Get unique socket values and trim any extra spaces
    const sockets = [...new Set(models.map(model => model.socket ? model.socket.trim() : model.socket))].filter(Boolean);
    sockets.sort();

    // Get unique category values and sort alphabetically
    const categories = [...new Set(models.map(model => model.cat))].filter(Boolean);
    categories.sort((a, b) => a.localeCompare(b));

    // Preserve current selections
    const currentSocket = socketFilter.value;
    const currentCategory = categoryFilter.value;

    // Clear existing options except the first one (All)
    while (socketFilter.options.length > 1) {
        socketFilter.remove(1);
    }

    while (categoryFilter.options.length > 1) {
        categoryFilter.remove(1);
    }

    // Add socket options
    sockets.forEach(socket => {
        const option = document.createElement('option');
        option.value = socket;
        option.textContent = socket;
        socketFilter.appendChild(option);
    });

    // Add category options
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });

    // Restore previous selections if they still exist
    if (currentSocket && sockets.includes(currentSocket)) {
        socketFilter.value = currentSocket;
    }

    if (currentCategory && categories.includes(currentCategory)) {
        categoryFilter.value = currentCategory;
    }
}

/**
 * Apply all filters to CPU models
 */
function applyFilters() {
    // If no CPU models data is available, return
    if (!window.cpuModelsData || !Array.isArray(window.cpuModelsData)) {
        return;
    }

    // Get filter values
    const searchInput = document.getElementById('cpu-models-search');
    const socketFilter = document.getElementById('cpu-models-socket-filter');
    const categoryFilter = document.getElementById('cpu-models-category-filter');

    const searchValue = searchInput ? searchInput.value.trim().toLowerCase() : '';
    const socketValue = socketFilter ? socketFilter.value : '';
    const categoryValue = categoryFilter ? categoryFilter.value : '';

    // Filter models based on all criteria
    const filteredModels = window.cpuModelsData.filter(model => {
        // Search filter
        const matchesSearch = !searchValue || Object.values(model).some(value =>
            String(value).toLowerCase().includes(searchValue)
        );

        // Socket filter - trim the socket value for comparison
        const matchesSocket = !socketValue || (model.socket && model.socket.trim() === socketValue);

        // Category filter
        const matchesCategory = !categoryValue || model.cat === categoryValue;

        // Model must match all active filters
        return matchesSearch && matchesSocket && matchesCategory;
    });

    // Display filtered models
    displayCpuModels(filteredModels);

    // Update the stats container with the count of visible items
    const cpuStatsContainer = document.getElementById('cpu-stats-container');
    if (cpuStatsContainer) {
        const totalCount = window.cpuModelsData.length;
        const visibleCount = filteredModels.length;

        if (visibleCount < totalCount) {
            cpuStatsContainer.innerHTML = `Showing ${visibleCount} of ${totalCount} CPU Models`;
        } else {
            cpuStatsContainer.innerHTML = `Total CPU Models: ${totalCount}`;
        }
    }
}

/**
 * Initialize Custom Rules functionality
 */
function initCustomRules() {
    // Elements
    const customRulesTab = document.getElementById('custom-rules-tab');
    if (!customRulesTab) return; // Exit if custom rules tab doesn't exist

    const addRuleBtn = document.getElementById('add-rule-btn');
    const ruleModal = document.getElementById('rule-modal');
    const ruleForm = document.getElementById('rule-form');
    const ruleFormCancel = document.getElementById('rule-form-cancel');
    const ruleDeleteModal = document.getElementById('rule-delete-modal');
    const confirmRuleDelete = document.getElementById('confirm-rule-delete');
    const cancelRuleDelete = document.getElementById('cancel-rule-delete');

    // Match type and action type selectors
    const matchTypeSelect = document.getElementById('match-type');
    const actionTypeSelect = document.getElementById('action-type');

    // Add event listeners
    if (addRuleBtn) {
        addRuleBtn.addEventListener('click', () => {
            openRuleModal();
        });
    }

    // Match type change event
    if (matchTypeSelect) {
        matchTypeSelect.addEventListener('change', function() {
            updateFormFieldsVisibility();
        });
    }

    // Action type change event
    if (actionTypeSelect) {
        actionTypeSelect.addEventListener('change', function() {
            updateFormFieldsVisibility();
        });
    }

    // Close modals when clicking on the close button
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            ruleModal.style.display = 'none';
            ruleDeleteModal.style.display = 'none';
        });
    });



    // Cancel button in rule form
    if (ruleFormCancel) {
        ruleFormCancel.addEventListener('click', () => {
            ruleModal.style.display = 'none';
        });
    }

    // Cancel button in delete confirmation
    if (cancelRuleDelete) {
        cancelRuleDelete.addEventListener('click', () => {
            ruleDeleteModal.style.display = 'none';
        });
    }

    // Confirm delete button
    if (confirmRuleDelete) {
        confirmRuleDelete.addEventListener('click', () => {
            console.log('Confirm delete button clicked');
            console.log('currentRuleId at time of confirmation:', currentRuleId);

            if (currentRuleId) {
                console.log('Calling deleteRule with ID:', currentRuleId);
                deleteRule(currentRuleId);
            } else {
                console.error('Cannot delete rule: currentRuleId is null or empty');
                showNotification('Error: No rule ID found for deletion', 'error');
            }
        });
    }

    // Rule form submission
    if (ruleForm) {
        ruleForm.addEventListener('submit', (event) => {
            event.preventDefault();

            const ruleId = document.getElementById('rule-id').value;
            const matchType = document.getElementById('match-type').value;
            const namePattern = document.getElementById('name-pattern').value;
            const originalId = document.getElementById('original-id').value;
            const cpuCount = document.getElementById('cpu-count').value;
            const actionType = document.getElementById('action-type').value;
            const fieldToChange = document.getElementById('field-to-change').value;
            const newValue = document.getElementById('new-value').value;
            const isActive = document.getElementById('is-active').checked ? 1 : 0;

            // Get CSRF token
            const csrfToken = document.querySelector('#rule-form input[name="csrf_token"]').value;

            // Validate form based on match type and action type
            if (!validateRuleForm(matchType, actionType)) {
                return;
            }

            // Create rule data object
            const ruleData = {
                match_type: matchType,
                name_pattern: namePattern,
                original_id: originalId,
                cpu_count: cpuCount ? parseInt(cpuCount) : null,
                action_type: actionType,
                field_to_change: fieldToChange,
                new_value: newValue,
                is_active: isActive
            };

            if (ruleId) {
                // Update existing rule
                updateRule(ruleId, ruleData, csrfToken);
            } else {
                // Create new rule
                createRule(ruleData, csrfToken);
            }
        });
    }
}

/**
 * Update form fields visibility based on match type and action type
 */
function updateFormFieldsVisibility() {
    const matchType = document.getElementById('match-type').value;
    const actionType = document.getElementById('action-type').value;

    // Get form groups
    const namePatternGroup = document.getElementById('name-pattern-group');
    const originalIdGroup = document.getElementById('original-id-group');
    const cpuCountGroup = document.getElementById('cpu-count-group');
    const fieldToChangeGroup = document.getElementById('field-to-change-group');
    const newValueGroup = document.getElementById('new-value-group');

    // Reset required attributes
    document.getElementById('name-pattern').required = false;
    document.getElementById('original-id').required = false;
    document.getElementById('cpu-count').required = false;
    document.getElementById('field-to-change').required = false;
    document.getElementById('new-value').required = false;

    // Show/hide fields based on match type
    if (matchType === 'name') {
        namePatternGroup.style.display = 'block';
        originalIdGroup.style.display = 'none';
        cpuCountGroup.style.display = 'none';
        document.getElementById('name-pattern').required = true;
    } else if (matchType === 'name_cpu_count') {
        namePatternGroup.style.display = 'block';
        originalIdGroup.style.display = 'none';
        cpuCountGroup.style.display = 'block';
        document.getElementById('name-pattern').required = true;
        document.getElementById('cpu-count').required = true;
    } else if (matchType === 'original_id_cpu_count') {
        namePatternGroup.style.display = 'none';
        originalIdGroup.style.display = 'block';
        cpuCountGroup.style.display = 'block';
        document.getElementById('original-id').required = true;
        document.getElementById('cpu-count').required = true;
    }

    // Show/hide fields based on action type
    if (actionType === 'update') {
        fieldToChangeGroup.style.display = 'block';
        newValueGroup.style.display = 'block';
        document.getElementById('field-to-change').required = true;
        document.getElementById('new-value').required = true;
    } else if (actionType === 'delete') {
        fieldToChangeGroup.style.display = 'none';
        newValueGroup.style.display = 'none';
        document.getElementById('field-to-change').required = false;
        document.getElementById('new-value').required = false;

        // Set default values for hidden fields to ensure they're included in the request
        document.getElementById('field-to-change').value = 'socket'; // Use a default value
        document.getElementById('new-value').value = 'delete'; // Use a placeholder value
    }
}

/**
 * Validate rule form based on match type and action type
 *
 * @param {string} matchType - Match type
 * @param {string} actionType - Action type
 * @return {boolean} True if valid, false otherwise
 */
function validateRuleForm(matchType, actionType) {
    // Validate match type
    if (matchType === 'name') {
        if (!document.getElementById('name-pattern').value.trim()) {
            showNotification('Name pattern is required for this match type.', 'error');
            return false;
        }
    } else if (matchType === 'name_cpu_count') {
        if (!document.getElementById('name-pattern').value.trim()) {
            showNotification('Name pattern is required for this match type.', 'error');
            return false;
        }
        if (!document.getElementById('cpu-count').value) {
            showNotification('CPU count is required for this match type.', 'error');
            return false;
        }
    } else if (matchType === 'original_id_cpu_count') {
        if (!document.getElementById('original-id').value.trim()) {
            showNotification('Original ID is required for this match type.', 'error');
            return false;
        }
        if (!document.getElementById('cpu-count').value) {
            showNotification('CPU count is required for this match type.', 'error');
            return false;
        }
    }

    // Validate action type
    if (actionType === 'update') {
        if (!document.getElementById('field-to-change').value) {
            showNotification('Field to change is required for update action.', 'error');
            return false;
        }
        if (!document.getElementById('new-value').value.trim()) {
            showNotification('New value is required for update action.', 'error');
            return false;
        }
    } else if (actionType === 'delete') {
        // For delete action, we don't need to validate field-to-change and new-value
        // But we still need to make sure they're included in the request
        document.getElementById('field-to-change').value = 'socket'; // Use a default value
        document.getElementById('new-value').value = 'delete'; // Use a placeholder value
    }

    return true;
}

/**
 * Load custom rules
 * @param {boolean} forceReload - Whether to force reload the data even if it was loaded before
 */
function loadCustomRules(forceReload = false) {
    const customRulesList = document.getElementById('custom-rules-list');
    if (!customRulesList) return;

    // Check if the custom rules tab is active
    const customRulesTab = document.getElementById('custom-rules-tab');
    if (customRulesTab && !customRulesTab.classList.contains('active')) {
        // If the custom rules tab is not active, don't load the data yet
        // It will be loaded when the tab is activated
        return;
    }

    // If data is already loaded and we're not forcing a reload, return
    if (customRulesLoaded && !forceReload) {
        return;
    }

    // Show loading state if not already showing
    if (!customRulesList.querySelector('.loading-row')) {
        customRulesList.innerHTML = '<tr class="loading-row"><td colspan="8">Loading custom rules...</td></tr>';
    }

    // Get CSRF token from the rule form
    const formCsrfToken = document.querySelector('#rule-form input[name="csrf_token"]');
    // If not found in rule form, try the CPU data form
    const cpuDataFormToken = document.querySelector('#cpu-data-form input[name="csrf_token"]');
    // If not found in either form, try the socket-csrf-token element
    const csrfTokenElement = document.getElementById('socket-csrf-token');

    // Use the first available token
    const csrfToken = formCsrfToken ? formCsrfToken.value :
                     (cpuDataFormToken ? cpuDataFormToken.value :
                     (csrfTokenElement ? csrfTokenElement.value : ''));

    console.log('Loading custom rules with CSRF token:', csrfToken);

    // Fetch custom rules
    fetch(`api/custom-rules.php?action=list&csrf_token=${csrfToken}`)
        .then(response => {
            console.log('Custom Rules API Response Status:', response.status);
            return response.json();
        })
        .then(data => {
            // Log response data for debugging
            console.log('Custom Rules Response Data:', data);

            if (data.success && data.data && Array.isArray(data.data)) {
                // Display custom rules
                displayCustomRules(data.data);

                // Set the flag to indicate that custom rules have been loaded
                customRulesLoaded = true;
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                console.error('Failed to load custom rules:', data);
                customRulesList.innerHTML = `<tr class="no-data-row"><td colspan="8">Error: ${data.message || 'Failed to load custom rules'}</td></tr>`;
                showNotification(data.message || 'Failed to load custom rules', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading custom rules:', error);
            customRulesList.innerHTML = '<tr class="no-data-row"><td colspan="8">Error: Failed to load custom rules</td></tr>';
            showNotification('Failed to load custom rules', 'error');
        });
}

/**
 * Display custom rules in the table
 *
 * @param {Array} rules - Array of custom rules
 */
function displayCustomRules(rules) {
    const customRulesList = document.getElementById('custom-rules-list');
    if (!customRulesList) return;

    if (!rules || rules.length === 0) {
        customRulesList.innerHTML = '<tr class="no-data-row"><td colspan="8">No custom rules found. Click "Add New Rule" to create one.</td></tr>';
        return;
    }

    let html = '';

    // Define a mapping of field codes to human-readable names
    const fieldLabels = {
        'socket': 'Socket',
        'cat': 'Category',
        'tdp': 'TDP',
        'cpuCount': 'CPU Count',
        'cores': 'Cores',
        'secondaryCores': 'Secondary Cores'
    };

    rules.forEach(rule => {
        const isActive = parseInt(rule.is_active) === 1;
        // Get human-readable field name or use the original if not in the mapping
        const fieldLabel = fieldLabels[rule.field_to_change] || rule.field_to_change;

        // Get match type label
        let matchTypeLabel = 'Name';
        if (rule.match_type === 'name_cpu_count') {
            matchTypeLabel = 'Name + CPU Count';
        } else if (rule.match_type === 'original_id_cpu_count') {
            matchTypeLabel = 'Original ID + CPU Count';
        }

        // Get action type label
        const actionTypeLabel = rule.action_type === 'delete' ? 'Delete' : 'Update';

        // Get pattern or ID to display
        const patternOrId = rule.match_type === 'original_id_cpu_count' ? (rule.original_id || '') : (rule.name_pattern || '');

        // Get CPU count to display
        const cpuCount = rule.cpu_count ? rule.cpu_count : '-';

        html += `
            <tr data-rule-id="${rule.id}">
                <td>${escapeHtml(matchTypeLabel)}</td>
                <td>${escapeHtml(patternOrId)}</td>
                <td>${escapeHtml(cpuCount)}</td>
                <td>${escapeHtml(actionTypeLabel)}</td>
                <td>${rule.action_type === 'delete' ? '-' : escapeHtml(fieldLabel)}</td>
                <td>${rule.action_type === 'delete' ? '-' : escapeHtml(rule.new_value)}</td>
                <td>
                    <span class="status-badge ${isActive ? 'active' : 'inactive'}">
                        ${isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td class="actions-cell">
                    <button class="icon-button edit-rule" title="Edit Rule">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                    <button class="icon-button toggle-rule" title="${isActive ? 'Deactivate' : 'Activate'} Rule">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            ${isActive ?
                                '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>' :
                                '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>'
                            }
                        </svg>
                    </button>
                    <button class="icon-button delete-rule" title="Delete Rule">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                    </button>
                </td>
            </tr>
        `;
    });

    customRulesList.innerHTML = html;

    // Add event listeners to action buttons
    addRuleActionListeners();

    // Update the custom rules header with the count
    const customRulesHeader = document.querySelector('.custom-rules-header h4');
    if (customRulesHeader) {
        customRulesHeader.textContent = `Custom Rules (${rules.length})`;
    }
}

/**
 * Add event listeners to rule action buttons
 */
function addRuleActionListeners() {
    // Get the custom rules table
    const customRulesTable = document.querySelector('.custom-rules-table');
    if (!customRulesTable) return;

    // Edit buttons - only select within the custom rules table
    const editButtons = customRulesTable.querySelectorAll('.edit-rule');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Use 'this' instead of event.target to ensure we get the button
            const row = this.closest('tr');
            if (row && row.dataset.ruleId) {
                const ruleId = row.dataset.ruleId;
                editRule(ruleId);
            }
        });
    });

    // Toggle buttons - only select within the custom rules table
    const toggleButtons = customRulesTable.querySelectorAll('.toggle-rule');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Use 'this' instead of event.target to ensure we get the button
            const row = this.closest('tr');
            if (row && row.dataset.ruleId) {
                const ruleId = row.dataset.ruleId;
                const isCurrentlyActive = row.querySelector('.status-badge').classList.contains('active');
                toggleRuleActive(ruleId, !isCurrentlyActive);
            }
        });
    });

    // Delete buttons - only select within the custom rules table
    const deleteButtons = customRulesTable.querySelectorAll('.delete-rule');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Use 'this' instead of event.target to ensure we get the button
            const row = this.closest('tr');
            if (row && row.dataset.ruleId) {
                const ruleId = row.dataset.ruleId;
                confirmDeleteRule(ruleId);
            } else {
                console.error('Could not find rule ID for delete operation');
            }
        });
    });
}

/**
 * Open the rule modal for creating or editing a rule
 *
 * @param {Object|null} rule - Rule data for editing, or null for creating
 */
function openRuleModal(rule = null) {
    const ruleModal = document.getElementById('rule-modal');
    const ruleModalTitle = document.getElementById('rule-modal-title');
    const ruleId = document.getElementById('rule-id');
    const matchType = document.getElementById('match-type');
    const namePattern = document.getElementById('name-pattern');
    const originalId = document.getElementById('original-id');
    const cpuCount = document.getElementById('cpu-count');
    const actionType = document.getElementById('action-type');
    const fieldToChange = document.getElementById('field-to-change');
    const newValue = document.getElementById('new-value');
    const isActive = document.getElementById('is-active');

    // Set modal title and form values
    if (rule) {
        ruleModalTitle.textContent = 'Edit Rule';
        ruleId.value = rule.id;
        matchType.value = rule.match_type || 'name';
        namePattern.value = rule.name_pattern || '';
        originalId.value = rule.original_id || '';
        cpuCount.value = rule.cpu_count || '';
        actionType.value = rule.action_type || 'update';

        // Set field_to_change and new_value based on action_type
        if (rule.action_type === 'delete') {
            fieldToChange.value = 'socket'; // Default value for delete action
            newValue.value = 'delete'; // Placeholder value for delete action
        } else {
            fieldToChange.value = rule.field_to_change || '';
            newValue.value = rule.new_value || '';
        }

        isActive.checked = parseInt(rule.is_active) === 1;
    } else {
        ruleModalTitle.textContent = 'Add New Rule';
        ruleId.value = '';
        matchType.value = 'name';
        namePattern.value = '';
        originalId.value = '';
        cpuCount.value = '';
        actionType.value = 'update';
        fieldToChange.value = '';
        newValue.value = '';
        isActive.checked = true;
    }

    // Update form fields visibility
    updateFormFieldsVisibility();

    // Show modal
    ruleModal.style.display = 'block';
}

/**
 * Edit a rule
 *
 * @param {string} ruleId - Rule ID
 */
function editRule(ruleId) {
    // Get CSRF token
    const csrfTokenElement = document.querySelector('#rule-form input[name="csrf_token"]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.value : '';

    // Fetch rule data
    fetch(`api/custom-rules.php?action=get&id=${ruleId}&csrf_token=${csrfToken}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                openRuleModal(data.data);
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                showNotification(data.message || 'Failed to load rule data', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading rule data:', error);
            showNotification('Failed to load rule data', 'error');
        });
}

/**
 * Create a new rule
 *
 * @param {Object} ruleData - Rule data object
 * @param {string} csrfToken - CSRF token
 */
function createRule(ruleData, csrfToken) {
    // Create form data
    const formData = new FormData();
    formData.append('name_pattern', ruleData.name_pattern);
    formData.append('field_to_change', ruleData.field_to_change);
    formData.append('new_value', ruleData.new_value);
    formData.append('is_active', ruleData.is_active);
    formData.append('match_type', ruleData.match_type);
    formData.append('cpu_count', ruleData.cpu_count);
    formData.append('original_id', ruleData.original_id);
    formData.append('action_type', ruleData.action_type);
    formData.append('csrf_token', csrfToken);

    // Log form data for debugging
    console.log('Create Rule Form Data:', {
        name_pattern: ruleData.name_pattern,
        field_to_change: ruleData.field_to_change,
        new_value: ruleData.new_value,
        is_active: ruleData.is_active,
        match_type: ruleData.match_type,
        cpu_count: ruleData.cpu_count,
        original_id: ruleData.original_id,
        action_type: ruleData.action_type
    });

    // Send request
    fetch('api/custom-rules.php', {
        method: 'POST',
        body: formData
    })
        .then(response => {
            console.log('Create Rule Response Status:', response.status);
            return response.json();
        })
        .then(data => {
            // Log the response data for debugging
            console.log('Create Rule Response Data:', data);

            if (data.success) {
                // Close modal
                document.getElementById('rule-modal').style.display = 'none';

                // Show success notification
                showNotification(data.message, 'success');

                // Reload custom rules
                loadCustomRules(true);

                // Reset final data loaded flag
                finalDataLoaded = false;

                // Generate whitelisted data automatically
                generateFinalData();
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                // Log detailed error information
                console.error('Failed to create rule:', data);
                showNotification(data.message || 'Failed to create custom rule.', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating rule:', error);
            showNotification('Failed to create rule', 'error');
        });
}

/**
 * Update a rule
 *
 * @param {string} ruleId - Rule ID
 * @param {Object} ruleData - Rule data object
 * @param {string} csrfToken - CSRF token
 */
function updateRule(ruleId, ruleData, csrfToken) {

    // Create request data
    const requestData = {
        id: ruleId,
        name_pattern: ruleData.name_pattern,
        field_to_change: ruleData.field_to_change,
        new_value: ruleData.new_value,
        is_active: ruleData.is_active,
        match_type: ruleData.match_type,
        cpu_count: ruleData.cpu_count,
        original_id: ruleData.original_id,
        action_type: ruleData.action_type,
        csrf_token: csrfToken
    };

    // Create form data for POST request
    const formData = new FormData();
    for (const key in requestData) {
        formData.append(key, requestData[key]);
    }

    // Use the custom-rules.php endpoint with action=update
    formData.append('action', 'update');
    fetch('api/custom-rules.php', {
        method: 'POST',
        body: formData
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                document.getElementById('rule-modal').style.display = 'none';

                // Show success notification
                showNotification(data.message, 'success');

                // Reload custom rules
                loadCustomRules(true);

                // Reset final data loaded flag
                finalDataLoaded = false;

                // Generate whitelisted data automatically
                generateFinalData();
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                showNotification(data.message || 'Failed to update custom rule.', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating rule:', error);
            showNotification('Failed to update rule', 'error');
        });
}

/**
 * Toggle a rule's active status
 *
 * @param {string} ruleId - Rule ID
 * @param {boolean} isActive - Whether the rule should be active
 */
function toggleRuleActive(ruleId, isActive) {
    // Get CSRF token
    const csrfTokenElement = document.querySelector('#rule-form input[name="csrf_token"]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.value : '';

    // Create form data
    const formData = new FormData();
    formData.append('action', 'toggle');
    formData.append('id', ruleId);
    formData.append('is_active', isActive ? 1 : 0);
    formData.append('csrf_token', csrfToken);

    // Send request
    fetch('api/custom-rules.php', {
        method: 'POST',
        body: formData
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success notification
                showNotification(data.message, 'success');

                // Reload custom rules
                loadCustomRules(true);

                // Reset final data loaded flag
                finalDataLoaded = false;

                // Generate whitelisted data automatically
                generateFinalData();
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error toggling rule active status:', error);
            showNotification('Failed to update rule status', 'error');
        });
}

/**
 * Confirm deletion of a rule
 *
 * @param {string} ruleId - Rule ID
 */
function confirmDeleteRule(ruleId) {
    console.log('confirmDeleteRule called with ID:', ruleId);

    // Set current rule ID
    currentRuleId = ruleId;
    console.log('currentRuleId set to:', currentRuleId);

    // Show delete confirmation modal
    const modal = document.getElementById('rule-delete-modal');
    modal.style.display = 'block';
    console.log('Delete confirmation modal displayed');
}

/**
 * Handle the confirm rule delete button click
 * This is called directly from the onclick attribute in the HTML
 */
function handleConfirmRuleDelete() {
    console.log('handleConfirmRuleDelete called directly from HTML');
    console.log('currentRuleId:', currentRuleId);

    if (currentRuleId) {
        deleteRule(currentRuleId);
    } else {
        console.error('Cannot delete rule: currentRuleId is null or empty');
        showNotification('Error: No rule ID found for deletion', 'error');
    }
}

/**
 * Delete a rule
 *
 * @param {string} ruleId - Rule ID
 */
function deleteRule(ruleId) {
    // Log the delete operation
    console.log('Deleting rule with ID:', ruleId);

    // Get CSRF token
    const csrfTokenElement = document.querySelector('#rule-form input[name="csrf_token"]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.value : '';

    // Create request data
    const requestData = {
        id: ruleId,
        csrf_token: csrfToken
    };

    // Log the request data
    console.log('Delete rule request data:', requestData);

    // Send request
    fetch('api/custom-rules.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
        .then(response => {
            console.log('Delete rule response status:', response.status);
            return response.json();
        })
        .then(data => {
            // Log the response data
            console.log('Delete rule response data:', data);

            // Close delete confirmation modal
            document.getElementById('rule-delete-modal').style.display = 'none';

            if (data.success) {
                // Show success notification
                showNotification(data.message, 'success');

                // Reload custom rules
                loadCustomRules(true);

                // Reset final data loaded flag
                finalDataLoaded = false;

                // Generate whitelisted data automatically
                generateFinalData();
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                showNotification(data.message || 'Failed to delete custom rule.', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting rule:', error);
            document.getElementById('rule-delete-modal').style.display = 'none';
            showNotification('Failed to delete custom rule. Check console for details.', 'error');
        });
}

/**
 * Escape HTML special characters
 *
 * @param {*} unsafe - Unsafe value
 * @return {string} Safe string
 */
function escapeHtml(unsafe) {
    // Convert to string and handle null/undefined
    if (unsafe === null || unsafe === undefined) {
        return '';
    }

    // Convert to string if it's not already a string
    const str = String(unsafe);

    return str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

/**
 * Load final data from the API
 * @param {boolean} forceReload - Whether to force reload the data even if it was loaded before
 */
function loadFinalData(forceReload = false) {
    // If data is already loaded and we're not forcing a reload, return
    if (finalDataLoaded && !forceReload) {
        return;
    }

    const finalDataList = document.getElementById('final-data-list');
    if (!finalDataList) return;

    finalDataList.innerHTML = '<tr class="loading-row"><td colspan="9">Loading whitelisted data...</td></tr>';

    // Update stats
    const statsContainer = document.getElementById('final-data-stats-container');
    if (statsContainer) {
        statsContainer.textContent = 'Loading statistics...';
    }

    // Simple fetch with timestamp to prevent caching
    const url = 'api/whitelisted-data.php?action=get&_=' + new Date().getTime();

    // Fetch final data
    fetch(url, {
        method: 'GET',
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            console.log('Final data API response:', data);
            if (data.success) {
                // Check if there's a message
                if (data.message) {
                    finalDataList.innerHTML = `<tr class="no-data-row"><td colspan="9">${data.message}</td></tr>`;
                    showNotification(data.message, 'info');
                } else {
                    // Display final data
                    displayFinalData(data.data);
                }

                finalDataLoaded = true;

                // Update stats
                if (statsContainer) {
                    if (data.stats) {
                        statsContainer.innerHTML = `
                            <div class="results-count">Showing all <strong>${data.count}</strong> CPUs</div>
                            <div>Total: <strong>${data.count}</strong></div>
                            <div>Whitelisted: <strong>${data.stats.whitelisted}</strong></div>
                            <div>Rules Applied: <strong>${data.stats.rules_applied}</strong></div>
                        `;
                    } else {
                        statsContainer.innerHTML = `
                            <div><strong>${data.count}</strong> CPUs loaded</div>
                            <div class="results-count">Showing all <strong>${data.count}</strong> CPUs</div>
                        `;
                    }
                }
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                finalDataList.innerHTML = `<tr class="no-data-row"><td colspan="9">Error: ${data.message}</td></tr>`;
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error loading whitelisted data:', error);
            finalDataList.innerHTML = `<tr class="no-data-row"><td colspan="9">Error loading whitelisted data. Please try again.</td></tr>`;
            showNotification('Error loading whitelisted data: ' + error.message, 'error');
        });

    // Add event listener to generate button
    const generateButton = document.getElementById('generate-final-data-btn');
    if (generateButton) {
        generateButton.addEventListener('click', generateFinalData);
    }

    // Add event listeners to filters
    setupFinalDataFilters();
}

/**
 * Display final data in the table
 *
 * @param {Array} cpus Array of CPU objects
 */
function displayFinalData(cpus) {
    const finalDataList = document.getElementById('final-data-list');
    if (!finalDataList) return;

    if (!cpus || cpus.length === 0) {
        finalDataList.innerHTML = '<tr class="no-data-row"><td colspan="10">No CPU data found. Please import CPU data first.</td></tr>';

        // Clear filter dropdowns
        const socketFilter = document.getElementById('final-data-socket-filter');
        const categoryFilter = document.getElementById('final-data-category-filter');
        const ruleAppliedFilter = document.getElementById('final-data-rule-applied-filter');

        if (socketFilter) {
            socketFilter.innerHTML = '<option value="">All Sockets</option>';
        }

        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="">All Categories</option>';
        }

        if (ruleAppliedFilter) {
            ruleAppliedFilter.innerHTML = '<option value="">All</option><option value="true">Applied</option><option value="false">Not Applied</option>';
        }

        return;
    }

    finalDataList.innerHTML = '';

    // Populate filter dropdowns
    populateFinalDataFilterDropdowns(cpus);

    // Display all CPUs initially
    cpus.forEach(cpu => {
        const row = document.createElement('tr');
        row.className = 'final-data-row';
        row.setAttribute('data-id', cpu.id);
        row.setAttribute('data-socket', cpu.socket);
        row.setAttribute('data-category', cpu.category);
        row.setAttribute('data-whitelist', cpu.whitelist);
        row.setAttribute('data-rule-applied', cpu.rule_applied);

        // Format the row content
        row.innerHTML = `
            <td>${cpu.id}</td>
            <td>${cpu.name}</td>
            <td>${cpu.mark || 'N/A'}</td>
            <td>${cpu.tdp || 'N/A'}</td>
            <td>${cpu.socket || 'N/A'}</td>
            <td>${cpu.category || 'N/A'}</td>
            <td>${cpu.cpu_count || '1'}</td>
            <td>${cpu.core_display || cpu.cores || 'N/A'}</td>
            <td><span class="status-badge ${cpu.whitelist ? 'active' : 'inactive'}">${cpu.whitelist ? 'Yes' : 'No'}</span></td>
            <td><span class="status-badge ${cpu.rule_applied ? 'active' : 'inactive'}">${cpu.rule_applied ? 'Yes' : 'No'}</span></td>
        `;

        finalDataList.appendChild(row);
    });
}

/**
 * Populate filter dropdowns for final data
 *
 * @param {Array} cpus Array of CPU objects
 */
function populateFinalDataFilterDropdowns(cpus) {
    // Get unique socket values
    const sockets = [...new Set(cpus.map(cpu => cpu.socket))].filter(Boolean);
    sockets.sort();

    // Get unique category values and sort alphabetically
    const categories = [...new Set(cpus.map(cpu => cpu.category))].filter(Boolean);
    categories.sort((a, b) => a.localeCompare(b));

    // Populate socket dropdown
    const socketFilter = document.getElementById('final-data-socket-filter');
    if (socketFilter) {
        // Keep the first option (All Sockets)
        socketFilter.innerHTML = '<option value="">All Sockets</option>';

        // Add socket options
        sockets.forEach(socket => {
            const option = document.createElement('option');
            option.value = socket;
            option.textContent = socket;
            socketFilter.appendChild(option);
        });
    }

    // Populate category dropdown
    const categoryFilter = document.getElementById('final-data-category-filter');
    if (categoryFilter) {
        // Keep the first option (All Categories)
        categoryFilter.innerHTML = '<option value="">All Categories</option>';

        // Add category options
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category.charAt(0).toUpperCase() + category.slice(1); // Capitalize first letter
            categoryFilter.appendChild(option);
        });
    }
}

/**
 * Set up event listeners for final data filters
 */
function setupFinalDataFilters() {
    // Search input
    const searchInput = document.getElementById('final-data-search');
    const searchClearButton = document.getElementById('final-data-search-clear');

    // Filter dropdowns
    const socketFilter = document.getElementById('final-data-socket-filter');
    const categoryFilter = document.getElementById('final-data-category-filter');
    const whitelistFilter = document.getElementById('final-data-whitelist-filter');
    const ruleAppliedFilter = document.getElementById('final-data-rule-applied-filter');

    // Add event listeners
    if (searchInput) {
        searchInput.addEventListener('input', filterFinalData);
    }

    if (searchClearButton) {
        searchClearButton.addEventListener('click', () => {
            if (searchInput) {
                searchInput.value = '';
                filterFinalData();
            }
        });
    }

    if (socketFilter) {
        socketFilter.addEventListener('change', filterFinalData);
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterFinalData);
    }

    if (whitelistFilter) {
        whitelistFilter.addEventListener('change', filterFinalData);
    }

    if (ruleAppliedFilter) {
        ruleAppliedFilter.addEventListener('change', filterFinalData);
    }
}

/**
 * Filter final data based on search input and filter dropdowns
 */
function filterFinalData() {
    const searchInput = document.getElementById('final-data-search');
    const searchClearButton = document.getElementById('final-data-search-clear');
    const socketFilter = document.getElementById('final-data-socket-filter');
    const categoryFilter = document.getElementById('final-data-category-filter');
    const whitelistFilter = document.getElementById('final-data-whitelist-filter');
    const ruleAppliedFilter = document.getElementById('final-data-rule-applied-filter');
    const finalDataRows = document.querySelectorAll('.final-data-row');

    // Get filter values
    const searchValue = searchInput ? searchInput.value.toLowerCase() : '';
    const socketValue = socketFilter ? socketFilter.value : '';
    const categoryValue = categoryFilter ? categoryFilter.value : '';
    const whitelistValue = whitelistFilter ? whitelistFilter.value : '';
    const ruleAppliedValue = ruleAppliedFilter ? ruleAppliedFilter.value : '';

    // Show/hide clear button based on search value
    if (searchClearButton) {
        searchClearButton.style.display = searchValue ? 'block' : 'none';
    }

    // Filter rows
    finalDataRows.forEach(row => {
        const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const socket = row.getAttribute('data-socket');
        const category = row.getAttribute('data-category');
        const whitelist = row.getAttribute('data-whitelist');
        const ruleApplied = row.getAttribute('data-rule-applied');

        // Check if row matches all filters
        const matchesSearch = !searchValue || name.includes(searchValue);
        const matchesSocket = !socketValue || socket === socketValue;
        const matchesCategory = !categoryValue || category === categoryValue;
        const matchesWhitelist = !whitelistValue || whitelist === whitelistValue;
        const matchesRuleApplied = !ruleAppliedValue || ruleApplied === ruleAppliedValue;

        // Show or hide row based on filter matches
        if (matchesSearch && matchesSocket && matchesCategory && matchesWhitelist && matchesRuleApplied) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // Show "No results" message if no rows are visible
    const finalDataList = document.getElementById('final-data-list');
    if (finalDataList) {
        const visibleRows = Array.from(finalDataRows).filter(row => row.style.display !== 'none');

        // Update the stats container with the count of visible items
        const statsContainer = document.getElementById('final-data-stats-container');
        if (statsContainer) {
            // Get the current stats HTML
            const statsHtml = statsContainer.innerHTML;

            // Create the new count text
            let countText;
            if (visibleRows.length < finalDataRows.length) {
                countText = `Showing <strong>${visibleRows.length}</strong> of <strong>${finalDataRows.length}</strong> CPUs`;
            } else {
                countText = `Showing all <strong>${finalDataRows.length}</strong> CPUs`;
            }

            // Check if we already have a results-count div
            if (statsHtml.includes('results-count')) {
                // Replace the existing results-count
                statsContainer.innerHTML = statsHtml.replace(/<div class="results-count">.*?<\/div>/, `<div class="results-count">${countText}</div>`);
            } else {
                // Add the results-count at the end
                statsContainer.innerHTML = statsHtml + `<div class="results-count">${countText}</div>`;
            }
        }

        if (visibleRows.length === 0) {
            // Check if "No results" row already exists
            const noResultsRow = finalDataList.querySelector('.no-results-row');

            if (!noResultsRow) {
                // Create "No results" row
                const row = document.createElement('tr');
                row.className = 'no-results-row';
                row.innerHTML = '<td colspan="11">No results match your filters.</td>';
                finalDataList.appendChild(row);
            }
        } else {
            // Remove "No results" row if it exists
            const noResultsRow = finalDataList.querySelector('.no-results-row');
            if (noResultsRow) {
                noResultsRow.remove();
            }
        }
    }
}



/**
 * Generate final data
 */
function generateFinalData() {
    // Show loading message
    const finalDataList = document.getElementById('final-data-list');
    if (finalDataList) {
        finalDataList.innerHTML = '<tr class="loading-row"><td colspan="11">Generating whitelisted data...</td></tr>';
    }

    // Update stats
    const statsContainer = document.getElementById('final-data-stats-container');
    if (statsContainer) {
        statsContainer.textContent = 'Generating whitelisted data...';
    }

    // Disable generate button
    const generateButton = document.getElementById('generate-final-data-btn');
    if (generateButton) {
        generateButton.disabled = true;
        generateButton.textContent = 'Generating...';
    }

    // Simple fetch with timestamp to prevent caching
    const url = 'api/whitelisted-data.php?action=generate&_=' + new Date().getTime();

    // Send request to generate final data
    fetch(url, {
        method: 'GET',
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            console.log('Generate final data API response:', data);
            if (data.success) {
                // Check if there's a message about no CPU data
                if (data.message && data.message.includes('No CPU data found')) {
                    // Show info notification
                    showNotification(data.message, 'info');

                    // Update final data list
                    if (finalDataList) {
                        finalDataList.innerHTML = `<tr class="no-data-row"><td colspan="11">${data.message}</td></tr>`;
                    }
                } else {
                    // Show success notification
                    showNotification(data.message, 'success');

                    // Reload final data
                    loadFinalData(true);

                    // Regenerate all identifiers after whitelisting
                    regenerateAllIdentifiers();
                }
            } else {
                // Check if this is an unauthorized access error (session expired)
                if (data.message === 'Unauthorized access' || data.code === 'SESSION_EXPIRED') {
                    handleUnauthorizedAccess();
                    return;
                }

                // Show error notification
                showNotification(data.message, 'error');

                // Reset final data list
                if (finalDataList) {
                    finalDataList.innerHTML = '<tr class="no-data-row"><td colspan="11">Error generating whitelisted data. Please try again.</td></tr>';
                }
            }

            // Re-enable generate button
            if (generateButton) {
                generateButton.disabled = false;
                generateButton.textContent = 'Generate Whitelisted Data';
            }
        })
        .catch(error => {
            console.error('Error generating whitelisted data:', error);

            // Show error notification with the specific error message
            showNotification('Error generating whitelisted data: ' + error.message, 'error');

            // Reset final data list
            if (finalDataList) {
                finalDataList.innerHTML = '<tr class="no-data-row"><td colspan="11">Error generating whitelisted data. Please try again.</td></tr>';
            }

            // Re-enable generate button
            if (generateButton) {
                generateButton.disabled = false;
                generateButton.textContent = 'Generate Whitelisted Data';
            }
        });
}
